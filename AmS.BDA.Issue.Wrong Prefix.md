﻿---
title: "AmS.BDA.Issue.Wrong Prefix"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# AmS.BDA.Issue.Wrong Prefix

Some contracts with plant and leading plant in GLOBUS leads to a wrong prefix

202012071243 David 咨询 Globus prefix 问题，已转发 [刘俊](CRM/刘俊.md)

![image.jpg](assets/20201207124344-s43takj-image.jpg)

[张丽丽](CRM/张丽丽.md) : 

![image.jpg](assets/20201207162744-4gncgc5-image.jpg)

20201207

> In AmSupply (and inside CommonComponent CC-Globus) the combinations of plant and managing plant are used to identify the “local” contracts and the ones between MBCC and the local european supplier.
>
> For plant Shunyi ( 1046 ) this works fine due to that the contracts are maintained correct the following way :
>
> Globus plant 1046 and managing plant 0520 -> Contract MBCC and local supplier
>
> Globus plant 1046 and managing plant 1046 -> Contract for the plant Shunyi
>
> For plant 1040 BDA we identified inside IPT-System ( which will be replaced by AmSupply) no contracts for MBCC maintained in a similar way.
>
> Expected was that we also have contracts inside which have following combination :
>
> Globus plant 1040 and managing plant 0514
>
> But only combination 1040 / 1040 is inside which lead to that we cannot differentiate in future with AmSupply...
>
> So question, which will be from my point of view no IT-topic , is why contracts in BDA for MBCC are not maintained with plant combination 1040 / 0514 ?

