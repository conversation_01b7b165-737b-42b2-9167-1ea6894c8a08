﻿---
title: "Quota"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# Quota

作用测试:

[20221114 答复 Priority DisCo_PSV_Quota Arrangement Indicator changed by Background job_20221114_040525.zip](es://20221114%20答复%20Priority%20DisCo_PSV_Quota%20Arrangement%20Indicator%20changed%20by%20Background%20job_20221114_040525.zip)

![image](assets/image-20241210130645-dxak3jq.png)

‍

总结:

> 在设置为'S'时, 不管quota%如何设置, 都只根据source list生成SL.
>
> 如果valid-end设置得当, 可以做到新旧SA item的切换: 例如10月1日前是国产货源, 之后进口货源.
>
> 如果valid-end日期交叉, 则生成先生效的item行的SL
>
> 在设置为'4'时, 会按照%分割货源的SL, 但如果item的valid不生效, 则生成PR

## Quota Arrangement Usage - USEQU

![image](assets/image-20221201102850-hb5yn0q.png)​

零件默认为4，此时依托Quota。在做Vendor Change之后，该值不变。

随后如果做了Unloading Point Change，则会变成S。此时依托Source List的顺序，或是MRP Planning标记。

![image](assets/image-20221201105702-3uqh5rl.png)​

‍

