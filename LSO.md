﻿---
title: "LSO"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# LSO Project

## Summary (项目摘要)

LSO (Logistics Special Processes / Logistics Sonderprozesse) 是一个用于管理试装/原型车零件（主要是 AS 零件）订购的流程，特别适用于那些没有现有合同的零件。它通过一次性的计划协议（SA）和"Baulos"（批次）编号来实现批次订购，以确保试装车辆的工程质量（EQ）水平一致性。LSO 的关键特性包括与 START 系统集成以获取最新的 BOM 数据，使用 Baulos 号进行跟踪（在 ASN/HU 中可见），为特定订单（如 EX33 SA）启用自动 Call-Off，以及处理特定的物流流程，例如转移预留（Transfer Reservations）和 AOR 产线送料。

[2025-05-21 更新] 近期 START 系统中 BVA define 的取值范围发生了变化：

- "CPL1"变为"series"
- "plant"变为"ramp up"
- 空值可编辑为"clarification"或"not procurement-relevant"

该变化导致 BVA define 为变化项的零件在向 AMS 传输时发生错误。MBUSI 同类系统的解决方案是提 Ticket 并做 Customizing，建议本地也提 Ticket 处理。

目前系统已上线（Hypercare 阶段暗示了这一点）。当前的关键主题和挑战包括：解决附加费（Surcharge）数量在 MD04 中不显示的问题，优化自动消耗流程，处理通过 LSO 订购的 JIS 零件 TR 创建在错误区域（JIT 而非 JIS）的问题，以及监控 EQ 等级。Hypercare 阶段发现的问题包括转移预留（TR）未被删除等。

[2025-06-16 更新] BBAC LOG 团队报告 LSO 流程中创建 Dummy PRPO 导致 MRP 逻辑错误，未来试装件的 ASN 被错误地计入当前可用库存，从而造成系列生产物料短缺。团队已请求 COC 团队进行系统修改，以确保 Dummy PRPO 相关的转移预留（TR）不影响近期生产的可用库存。email:`Critical Issue with LSO Dummy PRPO Process Affecting MRP Calculation and Production Supply at BBAC`

重要进展：

- [2023-02-20] SP29 更新修复了转移预留（Transfer Reservations）的自动删除功能。
- [2023-05-29] 设定了用于自动发送 EX33 SA Call-Off 的后台作业。
- [2023-08-14] 讨论了 JIS 零件通过 LSO 订购时 TR 创建在 JIT 区域的问题及其原因。

### Key Topics / Modules

#### 已有 SA 的 JIS 零件的 TR 创建在 JIT 区域 (TR for JIS Parts with Existing SA Created in JIT Area)

- 问题描述: 当某个车型上的序列化 JIS 零件通过 RD LOCAL 方式（使用 LSO）订购时，由于 LSO 本身不考虑 JIS 逻辑，导致采购申请（PR）被创建到了 JIT 区域。即使在 JIS 区域手动创建了转移需求（TR），后续的 LSO 流程也需要人工干预来控制和消耗这个 TR。
- 背景: 在 [2023-08-14] 的会议中进行了讨论。详细原因见 Storyline 部分。
- 相关人员:[李瑞瑞](CRM/李瑞瑞.md),[房旭](CRM/房旭.md),[姚琼](CRM/姚琼.md)
- 相关邮件:见 Storyline [2023-08-14] 条目。

#### Surcharge 数量不显示在 MD04 (Surcharge Quantity Not Shown in MD04)

- 问题描述: CPL1 和 EX33 项目会接收来自 START 系统的附加费（Surcharge）需求。但是，这部分需求数量不会显示在 MD04（库存/需求清单）中。
- 相关截图:![image](assets/image-20230417110048-me8pzbd.png)![image](assets/image-20230417105901-6mys03u.png)
  (下图 MD04 应显示 12，实际显示 11)![image](assets/image-20230417105751-b56bzv7.png)

#### Automatic Consumption

- 需求: CBFC 在接收试装车消耗信息时，需要与普通车辆的反冲进行区分。iDoc 中需要包含`readiness status, line information and baulos assignment`。同时，PR/PO 零件在特殊的存储地点（SLOC）中反冲，不应将信息提供给 CBFC。(DRF`1000001107`, 相关 DRF`1000001390` 用于月度 WIP 排除 B 阶段订单)
- 临时方案: 手动为车辆添加 Code 100 作为区分标识。
- 参考文档:[20210813_Ramp-Up Target_LSO_Automatic Consumption.pptx](es://20210813_Ramp-Up%20Target_LSO_Automatic%20Consumption.pptx)

#### AOR Linefeeding

- 目的: 使用事务代码[/DA0/0290_AOR](Projects/AmS@BDA/蓝图%20&%20流程/AmS%20BDA%20T-Code%20List.md#20230201132026-9s5u5mc) 一次性创建配线转储单（TO）。标准的 AOR 流程遵循 FIFO（先进先出）原则指定零件。
- LSO 特殊需求: 在 LSO 流程中，进行 AOR 配线时需要能指定零件的 Baulos 号。(DRF`1000000699`)
- 参考文档:[92 Standard AOR.pptx](es://92%20Standard%20AOR.pptx)

#### Transfer Reservations

- 流程: 零件需求存在于 MRP 区域（MRP Area），而 LSO 订单在工厂 104。通过创建转移预留（TrnRes）来传递需求。
- 曾存在问题: 零件配线完成后，TrnRes 不会自动删除。
- 解决方案: 考虑过系统功能修改、手动删除、为 MRP 区域创建 ULP。最终通过 SP29 更新 ([2023-02-20]) 实现了自动删除功能。
- 参考文档:[LSO Transferreservation closing 08062022.pptx](es://LSO%20Transferreservation%20closing%2008062022.pptx)
- 相关截图:![image](assets/image-20230220150613-eth9xpg.png)

#### ZGS / EQ-level Monitor

- 背景: LSO 可以从 START 系统接收 EQ 要求，但仍需要人工进行核查。这个需求与[2019 年为 IPT 增加 ZGS 管理](daily%20note/2022/01/20220119.md#20220119144605-geannfj)的想法相关。
- 相关截图:![image](assets/image-20230818153600-vun3aoa.png)![image](assets/image-20230818153628-e1ajl0i.png)

#### 自动 Call OFF EX33 SA (Automatic Call Off for EX33 SA)

- 功能: 系统通过后台作业自动为 LSO 流程下的 EX33 计划协议（SA）发送 Call-Off。
- 作业名称:
  - PSW:`DA0/1046/23/F0100_EX33_LSO_SEND`
  - PSV:`DA0/104/23/F0100_EX33_LSO_SEND`
- 参考邮件:[20230529081605 AW Setup Job....msg](es://20230529081605%20AW%20Setup%20Job%20for%20Automatic%20Call%20Off%20Sending%20for%20LSO%20EX33%20orders.msg),[20230529081632 关于 EX33 SA call off....msg](es://20230529081632%20关于EX33%20SA%20call%20off自动发送的后台任务启动.msg)

### System Details & Process Flow

#### 范围

- LSO 主要针对 AS（Anlauf Serie - 投产启动系列）零件。BS（Bau Stufe - 建造阶段/原型）零件的需求不同（例如需要更多缓冲和测试），通常不在此流程范围内。![image](assets/image-20230216153654-vv4kfd4.png)
- 零件生命周期各部门参与情况对比全球其他工厂:![image](assets/image-20230201142038-5qumfx5.png)

#### 物流分布

- 0 号线（推测为装配线）[Issue-Check: 模糊引用 - “推测为装配线”表述不确定，建议明确] 位于 RD 的 192 大楼。
- 中央试装仓库（与 LSO 相关）位于 145 大楼，主要存放 AS 零件。BS、ENG（发动机）、BP（车身零件?）[Issue-Check: 模糊引用 - “车身零件?”表述不确定，建议明确] 随厂区存放。

#### 系统接口与数据

- AMS <-> START: LSO 与 START 系统建立接口，以实现数据的及时更新（如 BOM 变更）。` START 系统数据交互要点：![image](assets/image-20230201161516-v2ujwfm.png)
  - 在 AMS 的 WE02 中可查看 Idoc，Basic type:`/DA0/0130_START`。
  - 查看当前生效的 Start run 版本：T-code`/DA0/0130_ACTRUN` (默认自动激活最新版本)。
  - 查看 Baulos 零件清单：Table`/DA0/0050A_BLMAT`。![image](assets/image-20230410145614-0n5au1j.png)
  - 查看从 Start 接收到的数据内容：T-code`/DA0/0130_BLMAIN`。![image](assets/image-20230410145655-5w4fyy6.png)

#### 端到端流程 (E2E Process Flow)

_(基于 [20220401_AMS@BDA_Blueprint_E2E_E84_Ramp-up_V2.00.pptx](es://20220401_AMS@BDA_Blueprint_E2E_E84_Ramp-up_V2.00.pptx))_

1. 订单管理 & 需求创建 (Order Management & Demand Creation)
   1. Baulos 创建 & 维护 (Baulos Creation & Maintenance)`
      - 使用 T-code[/DA0/0050A_BAULOSDEF](Projects/AmS@BDA/蓝图%20&%20流程/AmS%20BDA%20T-Code%20List.md#20230201144828-iqyswt5) 创建 Baulos。
      - 为每个 Baulos 分配不同的 UNLP (卸货点)，额外的 UNLP 由德国 SC/WTO 团队创建。
      - 在 T-code 中匹配 Baulos 和 UNLP。当 Baulos 完成并清理后，可以重复使用该 UNLP（需通知 WTO）。
   2. 检查车辆订单的 Baulos (Check Vehicle Orders for Baulos)
      - 使用 T-code`VELO`。
   3. 需求上传 (Demand Upload)：通过 SOB (推测为 System Order Buffer)。[Issue-Check: 模糊引用 - “推测为 System Order Buffer”表述不确定，建议明确]
   4. 调试零件 (BS 模块) (Commissioning Parts (BS modules))
      - 同一般零件，使用 SOB。
   5. 清理系统需求 (Clean System Demand)
      - 可通过`/DA0/0020_SOB_ENDAUS` 关闭，或启用自动冲减功能。
2. 零件订购 (Parts Ordering)
   1. DISCO IMS (初始物料设置): 参考[系统创建任务的逻辑](Projects/AmS@BDA/AMS.BDA.Training/操作文档%20-%20DISCO%20-%20Initial%20Material%20Setup.md#20230201154850-6zzgiz9)。
   2. Call Offs
      - 国产零件无合同时：由 RD 和 PS 通过 PRPO 订购。参考[START - Digital BOM](Projects/AmS@BDA/蓝图%20&%20流程/START%20-%20Digital%20BOM.md#20210218110215-o79tt1m)。
      - JIS 件：通过 PRPO 订购 (无特定 JIS CALL)。参见上文 "已有 SA 的 JIS 零件..." 问题。
      - 进口零件无合同时：通过 DISCO 中的 LSO 功能订购（需选择 Baulos Code）。系统会根据[同 Start 和 BOM 的接口信息](#20230201161132-2sf3ctb)给出 LSO 相关零件。![image](assets/image-20230201160814-si6vlez.png)
   3. 重新订购 (Reorder)
      - 系统提供 "check for changes" 功能来检查数量或日期的变动。
3. 收货 & 入库 (GR warehousing)
   1. 交货监控 (Delivery monitoring)
      - 序列化零件：使用 Shortage Monitor。
      - 试装零件 (pre-series)：使用 Trial Monitoring。
      - 注意：AMS 中无价格信息，用户提出的传送价格给 CBFC 的需求无法实现。
   2. 收货 (GR)
      - 在 BORGR 中维护 EQ 信息 (在 Daimler WE 标签页)。
      - 不支持系统索赔 (抱怨?)。
4. 产线供应 (Line Supply)：参见上文[AOR Linefeeding](#20230201131229-2nfiu9z) 主题。
5. 消耗 (Consumption)
   1. 方式：[Automatic Consumption](#20230201103755-zq5gnat) 或 手动 MIGO 过账。
   2. 库存修正不 Post 到 CBFC：参见[不 posting 到 CBFC 的库存修正](LSO%20go-live/不posting到CBFC的库存修正.md)。
6. 关闭流程 (Close)
   1. 触发条件：车辆全部达到状态 7000。
   2. 步骤：检查 Open Delivery -> 通知供应商和 WTO 检查清理待发货 -> 使用 SM30 清理 Table`/DA0/2080_BPHASE` -> 使用 T-CODE`/DA0/0050A_BAULOSDEF` 清理相关 BAULOS。
   3. 若有残留车辆，需要 CCPOM 进行清理。
   4. 剩余库存：如果可用于后续批次，需要调整 HU 信息。（[2023-02-09] Roland 承诺提供介绍 -_注意：此日期已过_）。[Issue-Check: 行动项待办 - StoryLine 已进展到 2025 年 6 月，但未见 Roland 介绍完成的相关记录]

## StoryLine (演进史)

📅20250616: BBAC LOG 团队报告 LSO 流程中创建 Dummy PRPO 导致 MRP 逻辑错误，未来试装件的 ASN 被错误地计入当前可用库存，造成系列生产物料短缺。已请求 COC 团队进行系统修改，以确保 Dummy PRPO 相关的 TR 不影响近期生产的可用库存。

📅20250521: START 系统 BVA define 取值范围变更（CPL1→series，plant→ramp up，空值可为 clarification/not procurement-relevant），导致部分零件信息传输 AMS 时出错。参考 MBUSI 经验，建议提 Ticket 并做 Customizing。

📅20230814: 李瑞瑞组织会议讨论"已有 SA 的 JIS 零件的 TR 创建在 JIT 区域"的问题。

- 起因邮件:[答复 回复：LCM\_ 使用 LSO 下 EX33 订单的车辆批次 (Jul~Sep.2023).msg](<es://答复%20回复：LCM_%20使用LSO下EX33订单的车辆批次%20(Jul~Sep.2023).msg>),[答复 ET 需求 - 202308141408.msg](es://答复%20ET需求%20-%20202308141408.msg)
- 讨论原因: (房旭和姚琼解释)
  1. 模型（Model）中包含 PRPO 件，导致整个模型需要通过 PRPO 订购。
  2. BVA 定义时为 PRPO 件，之后零件序列化了，但订购模式沿用旧模式。

📅20230529: 设置了用于 EX33 SA 自动 Call Off 发送的后台作业。

📅20230515: LSO Demostration Part 2 开始。

📅20230220: SP29 更新完成，修复了转移预留（Transfer Reservations）的自动删除功能。

📅20230112: LSO 项目启动会议。

📅20221201: 来自李妍妍的关于 LSO 的初步沟通。

📅2019: 曾有为 IPT 增加 ZGS 管理（与 EQ 等级监控相关）的想法。

## Reference (参考资料)

### 主要文件

- [20220401_AMS@BDA_Blueprint_E2E_E84_Ramp-up_V2.00.pptx](es://20220401_AMS@BDA_Blueprint_E2E_E84_Ramp-up_V2.00.pptx)
- [20210813_Ramp-Up Target_LSO_Automatic Consumption.pptx](es://20210813_Ramp-Up%20Target_LSO_Automatic%20Consumption.pptx)
- [92 Standard AOR.pptx](es://92%20Standard%20AOR.pptx)
- [LSO Transferreservation closing 08062022.pptx](es://LSO%20Transferreservation%20closing%2008062022.pptx)
- [2023.01.12_LSO_Kickoff_Meeting.pdf](es://2023.01.12_LSO_Kickoff_Meeting.pdf)
- [答复 回复：LCM\_ 使用 LSO 下 EX33 订单的车辆批次 (Jul~Sep.2023).msg](<es://答复%20回复：LCM_%20使用LSO下EX33订单的车辆批次%20(Jul~Sep.2023).msg>)
- [答复 ET 需求 - 202308141408.msg](es://答复%20ET需求%20-%20202308141408.msg)
- [20230529081605 AW Setup Job....msg](es://20230529081605%20AW%20Setup%20Job%20for%20Automatic%20Call%20Off%20Sending%20for%20LSO%20EX33%20orders.msg)
- [20230529081632 关于 EX33 SA call off....msg](es://20230529081632%20关于EX33%20SA%20call%20off自动发送的后台任务启动.msg)

### 核心人员

- [李瑞瑞](CRM/李瑞瑞.md)
- [房旭](CRM/房旭.md)
- [姚琼](CRM/姚琼.md)
- [李妍妍](CRM/李妍妍.Lydia.md)
- 李天宇
- Roland (在 StoryLine 中提及)

### 相关文档

- [Projects/AmS@BDA/蓝图 & 流程/AmS BDA T-Code List.md](Projects/AmS@BDA/蓝图%20&%20流程/AmS%20BDA%20T-Code%20List.md)
- [Projects/AmS@BDA/AMS.BDA.Training/操作文档 - DISCO - Initial Material Setup.md](Projects/AmS@BDA/AMS.BDA.Training/操作文档%20-%20DISCO%20-%20Initial%20Material%20Setup.md)
- [Projects/AmS@BDA/蓝图 & 流程/START - Digital BOM.md](Projects/AmS@BDA/蓝图%20&%20流程/START%20-%20Digital%20BOM.md)
- [daily note/2022/01/20220119.md#20220119144605-geannfj](daily%20note/2022/01/20220119.md#20220119144605-geannfj)
- [LSO go-live/不 posting 到 CBFC 的库存修正.md](LSO%20go-live/不posting到CBFC的库存修正.md)
- [用户账号权限审计/AMS 账号权责一致性审计\_2023.md#20240205111227-zwsc5qv](用户账号权限审计/AMS账号权责一致性审计_2023.md#20240205111227-zwsc5qv) (在 JIS GR & GI correction 功能加强.md 中提及，与 LSO 间接相关)

### 系统与技术细节

- **事务代码 (T-Codes)**:
  - `/DA0/0130_START`: 查看 Idoc [Issue-Check: 模糊引用 - 路径可能需要调整]
  - `/DA0/0130_ACTRUN`: 查看激活的 START run 版本
  - `/DA0/0130_BLMAIN`: 查看从 START 接收的数据
  - `/DA0/0050A_BAULOSDEF`: 创建/维护/清理 Baulos
  - `/DA0/0290_AOR`: AOR 产线送料
  - `/DA0/0020_SOB_ENDAUS`: 管理 SOB 需求
  - `VELO`: 检查车辆订单的 Baulos
  - `WE02`: iDoc 监控
  - `MD04`: 库存/需求清单
  - `BORGR`: 带 EQ 信息收货
  - `MIGO`: 手动货物移动/消耗
  - `SM30`: 表维护 (用于`/DA0/2080_BPHASE`)
- **数据表 (Tables)**:
  - `/DA0/0050A_BLMAT`: Baulos 零件清单
  - `/DA0/2080_BPHASE`: 关闭流程时清理
  - `/DA0/0130_BVAMAP`: Mapping of BVA defined text from START to LOG or NOT LOG
- **后台作业 (Background Jobs - EX33 Auto Call-Off)**:
  - PSW:`DA0/1046/23/F0100_EX33_LSO_SEND`
  - PSV:`DA0/104/23/F0100_EX33_LSO_SEND`
- **DRF 编号**:
  - `1000001107` (Automatic Consumption)
  - `1000001390` (关联自动消耗 - 排除 B 阶段 WIP)
  - `1000000699` (AOR Linefeeding)
- **关键概念**:
  - Dummy PRPO
  - MRP 逻辑
  - ASN (Advanced Shipping Notice)
  - TR (Transfer Reservation)

### BVA define 取值变更说明（2025-05-21）

- old: CPL1 → new: series
- old: plant 104/1046 → new: ramp up
- old: 空值 → new: clarification / not procurement-relevant
- 该变更导致部分零件信息传输 AMS 时出错，需提 Ticket 并做 Customizing（参考 MBUSI 经验）。

