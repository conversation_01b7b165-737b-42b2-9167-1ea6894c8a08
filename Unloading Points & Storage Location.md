﻿---
title: "Unloading Points & Storage Location"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# Unloading Points & Storage Location

# UNLP Migration from IPT to AMS

## 猸愶笍 Key

1. MFA 澶栧簱宸茬粡鍋滅敤, 涓旂洰鍓嶆棤澶嶇敤璁″垝.

2. [馃搮20210420 Speical Order ULP delete](#20210430104944-t5tx8aw)

### Unloading Point

#### JIS 闆朵欢鍗歌揣鐐?20210329

| line      | Description                              | MRP Area   | New AmS ULP |
| --------- | ---------------------------------------- | ---------- | ----------- |
| MFA / MF1 | MF1 Regular JIS Storage Location         | 104_AS4J0  | 3J0X        |
| MFA / MF1 | MF1 JIS Multisourcing Storage Location 1 | 104_AS4J1  | 3J1X        |
| MFA / MF1 | MF1 JIS Multisourcing Storage Location 2 | 104_AS4J2  | 3J2X        |
| MRA / MR1 | MR1 Regular JIS Storage Location         | 104_AS1J0  | 8J0X        |
| MRA / MR1 | MR1 JIS Multisourcing Storage Location 1 | 104_AS1J1  | 8J1X        |
| MRA / MR1 | MR1 JIS Multisourcing Storage Location 2 | 104_AS1J2  | 8J2X        |
| MRA / MR2 | MR2 Regular JIS Storage Location         | 104_AS2J0  | 9J0X        |
| MRA / MR2 | MR2 JIS Multisourcing Storage Location 1 | 104_AS2J1  | 9J1X        |
| MRA / MR2 | MR2 JIS Multisourcing Storage Location 2 | 104_AS2J2  | 9J2X        |
| MRA / MX2 | MX2 Regular JIS Storage Location         | 104_AS1AJ0 | 2J0X        |
| MRA / MX2 | MX2 JIS Multisourcing Storage Location 1 | 104_AS1AJ1 | 2J1X        |
| MRA / MX2 | MX2 JIS Multisourcing Storage Location 2 | 104_AS1AJ2 | 2J2X        |

#### Unloading point 313

![image.jpg](assets/image-20210607184157-ywgfusm.jpg)

鈥?
#### Bumper at 324

鐩墠鍦?324 鍗歌揣鐐圭殑 Bumper 闆朵欢渚?`A 293 880 23 01         5896`, 鐗╃悊閫佽揪 MRA1 瑁呯剨搴撴埧, 璐ㄩ噺鏌ラ獙, 涔嬪悗鎵嬪伐鏍规嵁鐢熶骇璁″垝浠?JIS 褰㈠紡閰嶇嚎, 鍗曠嫭鍗歌揣鐐规槸鍥犱负 Bumper 瑕佸崟鐙鍦ㄤ竴涓泦瑁呯鍐? 鍥犲巻鍙茶川閲忛棶棰樹弗閲?

[Anna 寤鸿鏇存敼涓?ULP 213, 鐩墠娌℃湁闆朵欢浣跨敤璇ュ嵏璐х偣銆備絾瀹冪殑 MRP Area 鏄?MRP_9, Bumper 鐨勫嵏璐х偣鍜屼娇鐢ㄤ綅缃?涓嶅缓璁缃垚涓€鏍枫€俔(#20210708184443-w5j9l37)

[馃搮20210824 浠?IT 鏉ㄤ竴楦ｅ寰楀埌娑堟伅, BGSL 灏嗕細淇濈暀.](#20210928092408-0g20zxv)

#### 327X MFA 鎺ユ敹 MRA 鐧借溅韬?
> 鐗╃悊鍗歌揣鐐规槸 MFA 瑁呯剨瑗夸晶鐨勯棬寤婁笅闈紝鍜?V205 鐩稿悓锛屼笖杩欎釜鍗歌揣鐐瑰彧鐢ㄤ簬鎺ユ敹鐧借溅韬紝涓嶄細鍜屽叾浠栫殑浠舵贩鐢紝鏄鐒婄墿娴佺洿鎺ユ帴鏀讹紝V206 瑙勫垝锛岃鐒婄墿娴佸弬涓庤璁恒€?
![image.png](assets/image-20211109112111-70a8qwp.png)

#### 342 in IPT for Headunit

[Storage location BGIDUnloading point 342](钃濆浘%20&%20娴佺▼/Cross%20Delivery/Planned%20Cross%20Delivery%20for%20Series%20Material.md#20211108105903-ofmgm0j)

### Storage Location

#### BGSF

璇?SL 鐢ㄤ簬涔板崠浠剁殑璁㈣喘, 杩欐牱璁㈣喘鏉ョ殑闆朵欢涓嶄細琚鍏?MRP 鐩稿叧.

#### BLSJ

濞熷璇存湁涓€浜涘崠浠舵病鏈?HU锛屾墍浠ュ厛鍋氬埌鎶ュ簾 location 锛屽啀鍋氬嚭鍘汇€?
[20210821 绛斿 鍜ㄨ BGSF 鍗栦欢闂.msg](es://20210821%20绛斿%20鍜ㄨ%20BGSF%20鍗栦欢闂.msg)

鈥?
## 馃搯 Storyline

### Bumper at 324

馃搮20210218 N293 Bumper in MRA1, External WH710/720

[澶栧簱 710 720 鍋滅敤.eml](assets/20210218131456-fggz56e-澶栧簱710,%20720鍋滅敤.eml)

N293 Bumper `A 293 880 23 01         5896`浣滀负杩涘彛 JIS 浠?
> physical GR in MRA1 warehouse (WH200/WH800), but system in WH710.
>
> QM inspector sends BUMPER require email to MRA1 warehouse keeper according to production planning.
>
> MRA1 warehouse keeper transfer BUMPERs to QM area and transfer these HUs to storage location: BGST and BSQU (QM area), if these BUMPER quality is OK , QM inspector will send email to warehouse keeper which HUs are OK and can transfer to lineside.
>
> Then warehouse keeper transfer these BUMPER' HU to lineside storage location: BASS

馃搮20210309 鏍规嵁鍚岀帇鏄婃槑鍜屽瓩鑰€骞崇‘璁? MFA 澶栧簱宸茬粡鍋滅敤, 涓旂洰鍓嶆棤澶嶇敤璁″垝.

馃搮20210311 Meeting with MRP, [Anton Weber](CRM/Anton%20Weber.md)鍚戝紶鍢夊拰閫瓉灞曠ず鍜岃鍒掓矡閫氬悗鐨勫嵏璐х偣娓呭崟骞惰璁?324 闂.

馃搮20210708 妫€鏌?Bumper 鍗歌揣鐐瑰垏鎹㈢殑浜嬫儏, 鍙戠幇 MRP 閭规旦骞朵笉鐭ユ儏.

鑱旂郴 WP4 鍜ㄨ涔嬪墠鏄€庝箞鍟嗛噺鐨? 闇€瑕佸敖蹇悓 WTO 鍐嶆鍗忓晢, 璧跺湪鍐荤粨鍖哄墠瀹屾垚鍒囨崲.

`RE RE 杞彂锛氱瓟澶?澶栧簱 710 720 鍋滅敤.msg`

Anna 寤鸿鏇存敼涓?ULP 213, 鐩墠娌℃湁闆朵欢浣跨敤璇ュ嵏璐х偣銆備絾瀹冪殑 MRP Area 鏄?MRP_9, Bumper 鐨勫嵏璐х偣鍜屼娇鐢ㄤ綅缃?涓嶅缓璁缃垚涓€鏍枫€?
email Andreas 鍜ㄨ浠栫殑鎰忚銆?
馃搮20210709 鐩墠 PKMC 涓缃殑 BGSL, 濡傛灉璋冩暣 ULP 涔熼渶瑕侀€氱煡搴撴埧浜哄憳. MRA1 鐨?KU 鏄痆zhai-zhibin-zdn5fn](CRM/缈熷織褰?md)

![image.jpg](assets/image-20210709102159-4k379rq.jpg)

鐩墠 324-BGSL-MRP_5锛岃兘鍚︽洿鏀?BGSL 鍒?CNB1 鑰屼笉褰卞搷 call off 鍜?ASN锛?
![image.jpg](assets/image-20210709130342-yrsiw1d.jpg)

email 鍜ㄨ缈熷織褰? 寰楀埌鐢佃瘽绛斿瑙夊緱娌℃湁闂.

TO 涓婄湅, 鍒涘缓浜洪兘鏄?KNWH, 鑰屼竴鑸浂浠舵槸 PushButton 鍙欢.

![image.jpg](assets/image-20210709163647-xnkwzc5.jpg)

![image.jpg](assets/image-20210709163810-jcs4t1o.jpg)

馃搮20210824 浠?IT 鏉ㄤ竴楦ｅ寰楀埌娑堟伅, BGSL 灏嗕細淇濈暀.

`20210928 绛斿 Storage location BGSL - MRP Area refinement .msg`

馃搮20210915 妫€鏌ヨ縼绉绘暟鎹殑鏃跺€欏彂鐜?涓€浜?Bumper 闆朵欢 SPO 鏄湪 BGS3 鍖哄煙, 鍥犳浼氳 WP4 杩佺Щ.

宸茬粡鎻愰啋 MRP 杩欎釜闂, 闇€瑕佸叧娉ㄦ墜宸ヨ皟鏁磋鍗?`20210915 杞彂 Final alignment which Material is needed in PSV.msg`

馃搮20210928 缁忓挩璇㈢▼鍔犳灄, SPO 浼氭湁鍗曠嫭鐨勮〃鏍肩瓫閫夋帶鍒?

鈥?
### ULP Update

馃搮20210420 Speical Order ULP delete

[鍥炲**Information_Sharing_WP4_IT_AmS_BDA\_\_**Status_of_Unloading_Points_in_AmSupply_BDA.msg](assets/鍥炲__Information_Sharing_WP4_IT_AmS_BDA____Status_of_Unloading_Points_in_AmSupply_BDA-20210430104936-dk1jsvy.msg)

馃搮20210617 IPT 涓?307 鍜?308 鐢ㄤ簬 DSP 闆朵欢.

[RE\__308 鍗歌揣鐐逛綔鐢╛.msg](assets/RE__308鍗歌揣鐐逛綔鐢╛-20210617145135-twxlxil.msg)

馃搮20210621 鍚岀墿娴佽鍒掞紝MM锛屼細璁悗锛屼細璁邯瑕併€傛洿鏀?IPT 绯荤粺 ULP 鎻忚堪锛屽幓鎺?JIS 瀛楁牱銆?
[Change_the_description_of_unloading_point_in_IPT.msg](assets/Change_the_description_of_unloading_point_in_IPT-20210621135900-jxsczcl.msg)

鈥?
# Change of Unloading Point in DISCO

## 猸愶笍 Key

/  
/

## 馃搯 Storyline

馃搮20210821 鐩墠 UAT 娴嬭瘯涓彂鐜扮殑涓や釜闂 `20210821 鍥炲 Unloading point change function does not work.msg`

1. DISCO 娴佺▼瀹屾垚,浣嗘槸 SA 娌℃湁瀹為檯琚洿鏀? Kim 灏嗕細鍦ㄤ笅涓€涓?Sprint 涓皾璇曚慨澶?
2. 浣跨敤 Automatic assignment 鏃? 鏃犳硶姝ｇ‘鐨勬寚娲?MRP Area, 瀵艰嚧澶氫釜涓嶅悓 UNLP 浣嶇疆鐨?item 琚垽鏂负鍚屼竴涓?Area, `Several unloading points of the order are in MRP area 104_AS4`鈥嬧€?鏃犳硶缁х画.  
   鈥?[image.png](assets/image-20210821135256-kitk6jh.png)鈥?
鈥?
## 馃敆 Reference

/  
/


