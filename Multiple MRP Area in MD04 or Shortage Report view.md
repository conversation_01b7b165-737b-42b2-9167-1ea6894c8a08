﻿---
title: "Multiple MRP Area in MD04 or Shortage Report view"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# Multiple MRP Area in MD04 or Shortage Report view

## 猸愶笍 Key

T-Code锛歚/DFJ/MD04` 

DRF `1000001406`

椤圭洰缁?budget

[DRF - Enhancement - multiple MRP Area in MD04 or Shortage Report view - 20211213.docx](es://DRF%20-%20Enhancement%20-%20multiple%20MRP%20Area%20in%20MD04%20or%20Shortage%20Report%20view%20-%2020211213.docx)

[馃搮20220516 鍔熻兘涓婄嚎 20220516 Multiple MRP Area in one View - DA00080_MD04.msg](#20221125144335-af16k7g)

## 馃搯 Storyline

馃搮20211213 DRF 鎻愪氦缁欏垬淇婏紝鍒樹繆宸叉彁浜ょ郴缁熴€?
Effort鎻忚堪锛?
> Additional efforts and possible miscalculation in urgent cases for material used in multiple plants.
>
> In the estimation MRP present to the E3:
>
> 70% materials are used in multiple plants (common parts between vehicle models, or vehicle model is produced in several plants).
>
> 20% materials needs daily check in MD04/shorate report.
>
> Each, in average, need 75 second for MRP to calculate stock/demand/delivery with multiple plants. (only time for check in multiple MD04/ShortageReport windows, not include delivery re-arrange, tracking, etc.)
>
> On currently AMS@MFA, they need additinoal 625 mintues daily (1.3 HC). After AMS@MRA golive, this number will be doubled.

馃搮20220516 鍔熻兘涓婄嚎 [20220516 Multiple MRP Area in one View - DA00080_MD04.msg](es://20220516%20Multiple%20MRP%20Area%20in%20one%20View%20-%20DA00080_MD04.msg)

鈥?

