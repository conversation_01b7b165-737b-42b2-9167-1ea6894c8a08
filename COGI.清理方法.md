﻿---
title: "COGI.清理方法"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# COGI.清理方法

# Key

[AMS@BDA_WP2_Solution Description_20201112_V0.6.xlsx](COGI%E6%B8%85%E7%90%86%E6%96%B9%E6%B3%95%2089e6f978322440629606045ce7b02d6b/AMSBDA_WP2_Solution_Description_20201112_V0.6.xlsx)

AMS 清理方式与 IPT 相同

mvt 995/996 用于 AS, 261/262 用于 PS/BS/PT

AMS 不再像 IPT 那样必须需要 linefeed 数据来进行零件消耗

每 30 分钟进行一次反冲， 因此 COGI 数据更新频繁

# Storyline

Nov 13, 2020 李明奎邮件介绍 - 方法相同

[确认物料消耗报错的IT解决方案  confirmation of consumption (cogi) IT solution_9_11_2020.pdf](es://确认物料消耗报错的IT解决方案%20%20confirmation%20of%20consumption%20(cogi)%20IT%20solution_9_11_2020.pdf)

