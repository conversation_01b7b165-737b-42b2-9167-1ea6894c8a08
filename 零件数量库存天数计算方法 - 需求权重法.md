﻿---
title: "零件数量库存天数计算方法 - 需求权重法"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# 零件数量库存天数计算方法 - 需求权重法

# 概述

根据库房零件库存数量和未来一段时间(一般是两周)的日均零件消耗需求, 计算零件数量库存天数。

零件清单根据((20230306153950-4q0zc5q ''))进行预筛选。

零件单体库存天数为加权值，表现出其对整体库存的影响。总体库存为加权值求和，等于直接用总库存除以总需求。

# 操作步骤

## 一、数据获取与预处理

### 1. 库存数据获取与预处理

IPT: T-Code：SQ01-WM_USER-Warehouse Stock

AMS: T-Code：LX03

![image.png](assets/image-20220125084806-hos2w8c.png)

保留以下信息：库房号、Storage Type、零件号，可用库存

各个工厂管理科在实际操作时，可以只筛选导出自己工厂内相关的Warehouse Number和DOH相关Storage Type（例如排除Storage Type 480）。

请按Warehouse Number区分总装、装焊库房，请根据零件号补充KLT或是GLT信息，最后获得数据如下表：

|**WhN**|**Typ**|**Material**|**Avail.stock**|
| ------| -----| -----------------| ----|
|总装|KLT|A 205 735 19 65|32|
|装焊|GLT|A 177 620 75 00|42|
|装焊|GLT|A 177 620 17 00|6|
|总装|GLT|A 205 820 13 03|60|
|总装|KLT|A 205 735 19 65|32|

### 2. 零件需求数据下载与预处理

#### IPT

T-Code： /DFJ/10B_ROC_STOCK

![image.png](assets/image-20220125084823-hohbr1o.png)

1. 在这个区域中选择所需的MRP Area。需要注意的是，Plant内只能填写CNB1，而下面的MRP-Area内则填写其他区域（例如MRP_4,ENG_A2）。建议每次只导出一个MRP Area，并将结果分开保存，用于之后分别计算总装、装焊库房的DOH。  
    ![image.png](assets/image-20220125084834-z507wck.png)

2. 在该区域中输入零件号和Controller。这里建议使用之前导出的有库存的零件清单。同时在MRP Controller中剔除L*1（JIS件），C *（辅料），和其他非正常Controller（999，YYY，PT1，PS *）。  
    ![image.png](assets/image-20220125084841-pgunwfj.png)

3. 默认选择明天的日期作为起点，在下面的“Demand qty for next N workdays”中填写数字，例如7，则为导出接下来7个工作日的需求。  
    ![image.png](assets/image-20220125084849-ftk9v48.png)

数据保留零件号、Controller、Demand N

假设之前在“Demand qty for next N workdays”中填写是10，则需要用Demand N/10 = 日均需求。

根据MRP Controller对物料货源进行判断。L开头的是国产件，M\V\X开头的零件是进口件。

#### AMS

总装需求数据：T-Code：SQ00-Standard Area-WP3_MRP-PBID_PBED_MRP

装焊需求数据：T-Code：SQ00-Global Area-MRP_DEPREQ

得到以下数据（例）：

|**Material**|**MRP Cont.**|**日均需求**|
| -----------------| ------| ----|
|A 205 735 19 65|进口|12|
|A 177 620 75 00|国产|9|
|A 177 620 17 00|进口|3|
|A 205 820 13 03|国产|20|
|A 205 735 19 65|进口|15|

## 二、数据整合与DOH计算

将此前的库存数据和日均需求数据整合，在库存清单中剔除近期无需求的零件。

套用公式：$库存天数 = \frac{Σ零件库存}{Σ日均需求}$

对于以上公式需要计算六次，总装、装焊分别计算如下三组数据：

1. GLT零件筛选出来，计算其库存天数

2. KLT零件筛选出来，计算其库存天数

3. Storage Type为902/923/921的零件筛选出来，计算其库存天数

最终得到以下数据：

1. 总装GLT库存天数（含902/923/921）

2. 总装KLT库存天数（含902/923/921）

3. 总装902/923/921区域库存天数（不区分GLT/KLT）

4. 装焊GLT库存天数（含902/923/921）

5. 装焊KLT库存天数（含902/923/921）

6. 装焊902/923/921区域库存天数（不区分GLT/KLT）

# 常见问题解答

## 部分主数据举例

|**Warehouse NO**|**MRP Area**|**Plant**|
| -----------| --------| --------------------|
|200|MRP_2|MRA1 BS|
|800|MRP_4|MRA1 AS|
|500|MRP_6|MRA2 BS|
|900|MRP_6|MRA2 AS|
|600|ENG_A2|Engine Plant1|
|610|ENG_A2|Engine Plant2|
|300|MRP_5|MFA BS|
|310|MRP_5|MFA AS|
|710 & 720|MRP_5|MFA External WH|
|740|ENG_A2|Engine External WH|

# 文档信息

> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>
> 初次发布时间：2019-10-29
>
> 最后更新时间：2022-11-23
>
> 版本: V2.6

## 主要变更历史

增加AMS部分描述

