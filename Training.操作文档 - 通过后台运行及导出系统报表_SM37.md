﻿---
title: "Training.操作文档 - 通过后台运行及导出系统报表_SM37"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 操作文档 - 通过后台运行及导出系统报表_SM37

> 通过后台运行及导出系统报表

# 一、功能简介

在进行报表导出时，  经常会遇到数据量大而导致导出时间过长的问题，或是该报表需要定期、定时导出，在这种情况下，直接通过T—Code查看报表结果会耗费大量的时间和系统资源，也无法做到精确地定时下载。因此需要借助系统后台服务器进行后台的数据导出，事后登陆后台下载数据的方法来导出报表。

# 二、系统操作详解

（一）后台运行报表

大部分报表类T—Code都可以通过点击左上方的“Program”—“Execute in Background”来进入后台执行设置界面。

![clipboard.png](assets/clip_image002-20240703092017-6txifja.png)

在“Output Device”一栏中填入“locl”，注意小写。

![](assets/clip_image004-20240703092017-4qxkra3.png)

系统默认会导出80列的数据，如果报表的数据列数特别大，可以点击![](assets/clip_image005-20240703092017-xth0ytc.png)图标，如下图位置调整列数，最大可以修改为255。默认行数（Row）为65不需要修改，因为报告不是只显示65行，而是以65行为一段，之间用空行隔开。

![](assets/clip_image007-20240703092017-uaj70aw.jpg)

设置完成后通过![](assets/clip_image008-20240703092017-nz3tz2d.png)按钮进入下一个界面。在运行时间的设置上主要有两个选项，一是![](assets/clip_image009-20240703092017-bjwjga6.png)来立即运行报表，而是点击![](assets/clip_image010-20240703092017-p2su96w.png)后设置具体的日期和运行时间。设置完成后点击下方的![](assets/clip_image011-20240703092017-gjrne9r.png)按钮即可。之后该报表作为一个Job在后台开始运行。

![](assets/clip_image013-20240703092017-x7tn6hm.png)

通过勾选![](assets/clip_image014-20240703092017-syfuigq.png)，我们可以设置让该报表定期运行，勾选后选择![](assets/clip_image015-20240703092017-n2a1uzp.png)，在弹出的窗口中选择每小时运行一次（Hourly）等选项进行相应的设置。

![](assets/clip_image017-20240703092017-i5n6l4p.png)

（二）后台导出报表

通过T-Code:SM37，我们可以查看此前被放入后台运行的报表情况。一般数据会在后台保存3天左右。如下图，主界面中可以选择一些基本的筛选条件，例如用户名（默认为显示自己在后台运行的Job）、Job的状态(例如已计划\执行中\执行完成 等等)、以及Job的开始时间。

![](assets/clip_image019-20240703092017-o17hy9w.png)

根据上面设置的筛选条件，系统会列出所有符合条件的Job，可以在汇总页面看到各Job的基本信息，例如执行情况，开始时间、持续时长等等。点击相应Job后的![](assets/clip_image020-20240703092017-ws4gm29.png)按钮，进入报告详情的导出界面。

![clipboard.png](assets/clip_image022-20240703092017-a6tdsun.png)

如果是初次使用T-Code：SM37，需要在设置界面调整默认显示页数，尽量将蓝框区域内数字改大，否则报表无法显示完整。

![clipboard.png](assets/clip_image024-20240703092017-3aelqnn.png)

设置完成后点击![](assets/clip_image025-20240703092017-7sijul0.png)进入结果页。数据量较大时，需要等待一定的时间。之后通过正常的方式导出到Excel表即可。

![clipboard.png](assets/clip_image027-20240703092017-wsavmo0.png)

（三）特殊情况的处理

由于数据量巨大，有时会出现Job长时间运行，最终因超时被Cancel的情况。这种情况需要特别留意，因为Job导出时占用后台服务器资源，时间过长时会影响服务器整体运行速度，并有影响夜间Job Chain的可能。

建议在初次运营报表时，密切关注T-Code：SM37中的Job状态，对于长时间未完成的Job主动停止运行。在运行前如果预判数据量较大，也请尽量分批次进行，多个Job可以同时进行，提高速度。

# 三、文档作者信息

> 李天宇（<EMAIL>）
>
> 初次发布时间：2020-01-20
>
> 最后更新时间：2024-07-03
>
> 版本: V2

