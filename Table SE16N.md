﻿---
title: "Table SE16N"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# Table

测试机中，`&SAP_EDIT`​开启编辑模式![image](assets/image-20230410145251-b7bh88n.png)​

## 查找program的table来源

SE80 - PSV无权限, 可以在KSV中尝试.

‍

## 查看字段的Table名

F1

![image](assets/image-20230309140551-c28tgtm.png)

‍

## UNLP SLOC AREA更新

![image.png](assets/image-20220706144351-bqp5vpr.png)

[UNLP-SL-MRP - 20220706.tfl](es://UNLP-SL-MRP%20-%2020220706.tfl)

[SLOC和MRP Area对应关系](#20220323155307-qnpqrks)+[UNLP和SLOC的对应关系, UNLP描述](#20220323155357-sjxpihs)+[Storage Type和SLOC](#20221107152227-8cmbmgy)+[MRP Area与Building关系](#20221014084717-a1xmotl)+[卸货点和卸货道口的关系](#20221027152637-c4ciewh)

### UNLP和MRP Area对应关系

/DA0/2080_UNLP

### SLOC和MRP Area对应关系

MDLG

T001L - SLOC是否MRP Related, 有1的不算库存 📅20250603 HANA更新后不再需要, 因为所有非MRP相关的SLOC将进入ND MRP area

### UNLP和SLOC的对应关系, UNLP描述

/DA0/MM_ABLLFA

### Storage Type和SLOC

T320A

‍

### 卸货点和卸货道口的关系

T30C

### MRP Area与Building关系

/DA0/0090_HBERID

### 车型与Building关系

/DA0/0090_PRHALL

根据这个配置,在DISCO中会分配默认的卸货点.

# 操作记录

## Vehicle Action Point Time

/DCI/CCG_CHKPT。

在VELO中可以查询变动记录：[操作文档 - 查询车辆计划上线日期变动情况 - VELO](Projects/AmS@BDA/AMS.BDA.Training/操作文档%20-%20查询车辆计划上线日期变动情况%20-%20VELO.md)

![image](assets/image-20230718094655-z2bxf5i.png)​

## Material Posting

​`MSEG`​，类似MB51记录

![image](assets/image-20230718094359-qnxqyx8.png)`MKPF`​，Material Document

![image](assets/image-20230718094444-jzjqr3f.png)

## DISCO操作记录

`/DA0/2080_DCMARC` 中找到 DISCO Task No.，之后在通过 Task No 在 `/DA0/2080_DCSUPP` 中找到该 Task 的操作及删除记录。

## 试装车号

/DA0/0050A_BLFZG

内容同BEDZL中的试装车信息, 包括其装焊, 总装的上线时间.

## RFQ

/DCI/GLO_PRPOS

# 主数据

## SA数据

EKPO

## MRP Area数据

MDMA

## 零件DIALOG主数据 - 104

MARC

## AMSJ/R

/DCI/RQI_MB_MAS

## 零件库存及库存信息, 用于EDW Calculate Overflow

创建于Sprint25 [Sprint 25 Release Note.doc](es://Sprint%2025%20Release%20Note.doc)

LQUA

/DA0/0000_LAGPLQ

## Routing ID

/DA0/0320_ROUTNG

## GR & ASN in MD04

[EKBE（GR 101）与EKES（MRP ASN in MD04）不一致。](Projects/AmS@BDA/AMS.BDA.Training/AMS.问题解决/MD04收货与ASN%20不一致差异.md#**************-b9ggzip)

‍

## [筛选车辆Model-> TABLE = /DA0/MMIC_FIL, 相关零件分别传入IPT和AMS​image.png ...](Projects/AmS@BDA/AMS.BDA.Training/操作文档%20-%20DISCO%20-%20Initial%20Material%20Setup.md#**************-fmtt7v5)

‍

## [筛选BZA-> TABLE = /DA0/MMIC_UBZA](Projects/AmS@BDA/AMS.BDA.Training/操作文档%20-%20DISCO%20-%20Initial%20Material%20Setup.md#**************-u1l50p0)

‍

## PACKAGE, 零件包装信息

PACKKPS, 需要通过 # 拆分下 (数据不全)  
​![image](assets/image-**************-sfib99l.png)

VEKP, PackageInstru和HU的关系

‍

## OER

/DA0/0150_OERDAT

# 系统需求相关

## 查看零件历史TBE需求记录

/DA0/0050A_ARQFD：TBE-P

/DA0/0050A_ARQVD：TBE-T

下图例在2月12日更新了3月20日的TBE[-]()T为135pcs

![image](assets/image-20230213145949-z2l2hve.png)

## 车辆号、零件号及生产位置

/DA0/0050A_BLMAT baulous号、零件号及零件状态（40)  
​![image](assets/image-20230213152506-xskd8p5.png)​  

/DA0/0050A_BLSTL 零件号，车号，MRP Area  
​![image](assets/image-20230213152603-wxano62.png)

/DA0/0050A_BLfzg 车号，baulos，生产线  
​![image](assets/image-20230213152702-nqg9pdp.png)

前提是零件主数据及MRP Area数据维护正确

‍

# BOM

/DA0/0060_DIBKPO (类似CS15), 显示PEM, 但没有时间

![image](assets/image-20240920141306-0igtdhb.png)

