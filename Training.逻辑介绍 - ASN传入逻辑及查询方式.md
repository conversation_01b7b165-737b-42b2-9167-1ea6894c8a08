﻿---
title: "Training.逻辑介绍 - ASN传入逻辑及查询方式"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 逻辑介绍 - ASN传入逻辑及查询方式

# 现象及原因描述

在 AMS 体系下, ASN 分为 `Pre-ASN`​ 和 `Real ASN`​ 两种. 

一开始 GSS 会发送 `Pre-ASN`, 其中仅包含基本的零件号\数量\时间信息, `Pre-ASN` 不会直接显示在 MD04 和 Container-BLG-Report 中. 仅作为发货预告存在于系统中.

待 `Real ASN`​ 从 GSS 发出后, AMS 系统才会收到详细的运输信息(Vessel Name, Contrainer ID, etc.), 从而显示到 MD04 中参与 MRP Run.

‍

# 简单举例

如下图, Delivery Date 较远的 ASN 为 `Pre-ASN`, 其 Ext. Delivery ID 以 `PRE` 开头.

`Real ASN` 除了包含有更多的运输信息之外, 其 Item Category 为 `ZS01`. 只有 `ZS01` 类别的 ASN 才会被计入 MRP Run 考虑范围中. 因此该零件目前 MD04 中最远的一条 ASN 仅显示到 3 月 29 日.

![image.png](assets/image-20220214135145-5cdsdgy.png)

![image.png](assets/image-20220214135718-wo6f3im.png)

# 进阶讲解

## Pre-ASN 批量查询方式

通过 Delivery Status Report(`/DA0/0220_DLVRY_STAT`)可以做到批量查询 Pre-ASN 信息.

在选择 `Generic Selection` 的前提下,在 External Delivery ID 中使用 `PRE*` 参数.

![image.png](assets/image-20220214135947-dezyj34.png)

## MRP 数值查看及计算

`Pre-ASN` 的信息也会显示在 Shortage Report 及其 Delivery Status Report 中, 供 MRP 参考.

再次强调该数值并未参与 MRP Run 和 Shortage Report 的平衡表计算中. 因此该页面库存值和预警并未考虑 `Pre-ASN`, `OpenOrdQty` 也未进行相应的转换.

![image.png](assets/image-20220214141143-c3yeeeg.png)

‍

# 文档信息

> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>
> 初次发布时间：2022-02-14
>
> 最后更新时间：2022-02-14
>
> 版本: V0.9

‍

