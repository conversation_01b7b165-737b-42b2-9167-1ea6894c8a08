# 根据 Dennis 的邮件清理零件的 linefeeding数据

📅20220531 来自dennis的邮件, 之前太忙, 📅20220613开始联系物流规划

N000000004592, 从MD04数据可见, 在253/254上面为总装零件, 206上为装焊件. email 马超

| MRP  element | MRP element  data | Storage Location | MRP Area |
| ------------ | ----------------- | ---------------- | -------- |
| DepReq       | V 213             | BASA             | MRP_4    |
| DepReq       | V 253             | BASM             | MRP_6    |
| DepReq       | N 293             | BASS             | MRP_9    |
| DepReq       | V 213             | BASM             | MRP_6    |
| DepReq       | V 206             | BBS3             | MRP_2    |
| DepReq       | V 253             | BASS             | MRP_9    |
| DepReq       | A 206 600 23 01   | BBS3             | MRP_2    |
| DepReq       | V 254             | BASM             | MRP_6    |

[何小妹](CRM/何小妹.md)答复AS不需要该零件, 她会修改linefeed吗? 咨询中. 📅20220614.  📅20220615 答复说是系统自动生成的. 反馈dennis.

‍

A2546370800, 设置的总装linefeed, 系统需求也只有总装BASM, 但SA的卸货点是BGI5. emailed 于志敏.

可能是material category导致的, 调整试试看.

![image.png](../assets/image-20220613105258-pvolbvl.png)

A0009939202, 目前SA在总装, 零件同时用于AS/BS, 需要事后调整系统. emailed 邹浩.

‍
