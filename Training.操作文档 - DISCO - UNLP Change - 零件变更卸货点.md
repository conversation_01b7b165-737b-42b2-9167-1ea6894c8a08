﻿---
title: "Training.操作文档 - DISCO - UNLP Change - 零件变更卸货点"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 操作文档 - DISCO - UNLP Change - 零件变更卸货点

> 通过 `/DA0/DISCO` 中的 `Unloading point change / change of delivery method` 功能，可以更改或新建 SA Item 的卸货点及相关 SA 主数据。并通过设置 Item 的生效时间，实现需求位置或零件类别的变更。该命令通常作为 Vendor Change 操作的后续步骤来执行。

## 操作步骤

### DISCO

T-Code：`/DA0/DISCO`，选择 Unloading Point Change 流程

![image](assets/image-20221122154749-21uh2mq.png)

#### [100/110] Edit Worklist

点击 ![image](assets/image-20221122154904-r2zpncx.png) 创建任务，一般通过输入零件号创建任务。如果系统提示有黄灯、红灯不可创建任务，请参考系统最右侧的信息提示判断原因。

随后选中所有行，点击 ![image](assets/image-20221122155057-j4dlmqf.png)

![image](assets/image-20221122155142-2t189kk.png)

#### [190] Complete Tasks

选中一行作为主数据的基础，点击 ![image](assets/image-20221122155809-7guxdwq.png) 复制出一行新的。

选中不需要使用的 item 行，点击 Delete Position。（请放心只是在本次任务中不需要用到这些 item 行，所以将他们排除掉，并不是真的在系统中删掉了 SA Item）

这步操作之后，剩下的应该都是新建的 Item 行。

![image](assets/image-20221122155345-2onl0x8.png)

选中新建出来的 Item 行，点击 ![image](assets/image-20221122160644-tluh1nr.png) 进行单行修改或是点击 ![image](assets/image-20221122160658-mv91fwd.png) 进行多行批量修改。

在弹出的窗口中输入目标卸货点。

![image](assets/image-20221122161719-a00jch1.png)

选中新建出来的 Item 行，点击 ![image](assets/image-20221122161858-o5ti4qz.png)，设置其生效时间。生效时间只能是今天开始往后的日子，且工作日历中需要为工作日。

![image](assets/image-20221122161946-8e51i7j.png)

完成后任务状态变为 190，点击 ![image](assets/image-20221122162248-9hg3fs5.png) 进入下一步。

**批量维护小技巧：**

当进行批量维护时与单个处理在这一步最大的差异就是，利用好 Filter 得到你想要处理的数据，以新加进口 211X 例：

![image](assets/image-20230512093229-jfkp9ky.png)

上面每一个物料都有一到 3 个 Item, 但我们只想 copy 801X 到新的 211X, 这时我们可以利用 filter 删除我们不想要的 item。

如下图，选 UP 对应栏位，点 filter 按钮，选出我们想删除的 Item：

![image](assets/image-20230512093249-x5hhb84.png)

删除不要的 Item (建议删除与本次操作无关的 items, 一是减少之后操作的工作量, 而是因为程序会在之后的步骤中通过 'load default values' 导致 MRP AREA 下的主数据被覆写)：

![image](assets/image-20230512093256-gds9agp.png)

取消 Filter 后只有 801X, copy 所有 item ，新加状态为 110 的行项目，后续处理与单个没有本质差异：

![image](assets/image-20230512093312-tpybr5f.png)

**重点**：Copy 完成后，连原来的 801X 也可以删除，只保留新加 Item 进行后续处理。

![image](assets/image-20230512093312-tpybr5f.png)

#### [210/220] Assign MRP Areas

选中所有行，点击 ![image](assets/image-20221122162334-prlyrxl.png)，系统会根据此前输入的卸货点自动分配 MRP Area。

完成后任务状态变为 210，点击 ![image](assets/image-20221122162419-gocscdy.png) 进入下一步。

#### [240/250] Maintain MRP Fields

这一步为维护零件相关主数据，例如 MRP Controller ID，Safety Time，等等。

首先点击 ![image](assets/image-20221122162555-onre7qj.png) 读取系统默认值。

随后通过 ![image](assets/image-20221122162622-g3r645i.png) 分别进行单行更改或是批量更改。本步骤必须进行操作，如果不需要进行任何更改，也需要进入修改界面然后点击确认。需要注意的是 **JIS 卸货点维护时必须维护 JIS MRP Controller**，否则最后一步激活时会报错并导致该 SA 后续操作都需要手工进行纠错。

完成后任务状态变为 240，如果是 JIS 零件需要点击 ![image](assets/image-20221122162758-cd5gfql.png) 进入 JIS 相关主数据配置，否则可以直接进入 ![image](assets/image-20221122162814-0kxzydz.png) 步骤。

#### [260] JIS Component Group Master

仅 JIS 相关零件需要本操作。

- ![image](assets/image-20221122163113-ggzigzv.png) 维护单行 Item 的 CGM
- ![image](assets/image-20221122163127-gli65g4.png) 维护多行 Item 的 CGM
- ![image](assets/image-20221122163134-xo6ai29.png) 跳过，先不维护 CGM

随后进入 ![image](assets/image-20221122162814-0kxzydz.png) 步骤。

#### [900] Activate

进行最终的数据确认，如无问题，点击 ![image](assets/image-20221122163221-bcixxiu.png) 确认激活时间。

如需修改激活时间，点击 ![image](assets/image-20221122163259-vkw21s6.png)。

最后点击 ![image](assets/image-20221122163308-vea6hlb.png) 完成本任务。

完成后，如果激活日期是当日，则状态变为 900，如果激活日期是未来的某一天，则状态变为 600。

### MM02

上述步骤完成后在 MM02 如图位置检查零件的配置应该是 `s`

![image](assets/image-20230309092800-w0o94gq.png)

## 其他注意事项

1. 请在进行相关操作前后，同其他部门相关同事做好充分的沟通工作。国产零件如果是更改卸货点，需要联系 ATM 同事。
2. AMS 标准流程中, 这个功能会检查每个 MRP Area 的使用情况, 如果 MRP Area 中没有 SA 也没有设置相关的 SOBSL (Special procurement in MM02), 那么激活之后会给相应的 MRP Area 打上 deletion flag 标记。

## 常见问题解答

1. 在 [210/220] Assign MRP Areas 步骤点击 ![image](assets/image-20221122162334-prlyrxl.png) 时报错，报错信息 ![image](assets/image-20221122164649-ly9kcgp.png) 提示为 **Several Unloading Points of the order are in...**

   系统在进行数据核查时发现，同一个 MRP Area 下有多个 Item 行同时生效。请调整自己的数据配置。

2. 在 [210/220] Assign MRP Areas 步骤点击 ![image](assets/image-20221122162334-prlyrxl.png) 时报错，报错信息 ![image](assets/image-20221122164649-ly9kcgp.png) 提示为 **At least one MRP area could not be automatically assigned**

   系统识别到该零件在其他区域有 usage, 但是并未分配 ULP，则会提示需要给该区域分配卸货点。

   选中 Item 行项目，点击“manual assignment”，查看系统提示。

   对于确实不需要且没有 In Usage Exists 的行可以选中后点击 ![image](assets/image-20221122165312-gta8cgf.png) 删除。

   其他行有两种选择：

   - 删除 task 重新开始，在前面的步骤中 copy 出对应的卸货点 SA item 一起进行创建。
   - 对于有 Usage 的行，请点击 ![image](assets/image-20221122165342-upaja25.png)。这样系统会自动创建这个 MRP Area，并通过 SOBSL 把需求传递到指定的卸货点所在的另一个 MRP Area。

   ![image](assets/image-20221122165057-jwyenst.png)

   在激活完成后，**系统并不会给这部分 MRA Area 下创建 SA，而是通过 SOBSL 将需求指向到存在 SA 的 MRP Area。**

3. 常见报错 code：

   - `475`: Problem is that procurement type is E. This caused an error in quota arrangement.
   - `515`: Please check the partner profile in WE20.
   - `440`: The material master is not maintained completely. It seems as for material master creation a wrong reference material was selected.
   - `610`：JIS UNLP，MRP Controller 也需要是 JIS [回复\_ INC1697807 activate 700001173 to 900_16_11_2022.pdf](es://回复_%20INC1697807%20activate%20700001173%20to%20900_16_11_2022.pdf)

## 文档信息

> - 李天宇 ([<EMAIL>](mailto:<EMAIL>))
> - 初次发布时间：2020-10-16
> - 最后更新时间：2024-11-14
> - 版本: V1.5

