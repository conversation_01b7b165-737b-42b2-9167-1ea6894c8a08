﻿---
title: "低代码平台产品"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# 低代码平台

- [低代码平台](#低代码平台)
  - [Summary](#summary)
  - [StoryLine](#storyline)
  - [Reference](#reference)
    - [Key Personnel](#key-personnel)
    - [Related Documents](#related-documents)
  - [Developed Applications / Sub-Projects](#developed-applications--sub-projects)
    - [物流运营第三方信息综合看板](#物流运营第三方信息综合看板)
      - [Summary](#summary-1)
      - [Storyline](#storyline-1)
      - [Reference](#reference-1)
    - [第三方非生产时间加班审批 (LSP Overtime Approval)](#第三方非生产时间加班审批-lsp-overtime-approval)
      - [Summary](#summary-2)
      - [Storyline](#storyline-2)
      - [Reference](#reference-2)
    - [供应商绩效考核面板](#供应商绩效考核面板)
      - [Summary](#summary-3)
      - [Business Requirements Document (BRD00405) Details](#business-requirements-document-brd00405-details)
      - [Storyline](#storyline-3)
      - [Reference](#reference-3)
  - [待确认事项](#待确认事项)

---

## Summary

本文档记录了公司推广和应用低代码平台（简道云）的相关信息。
核心目标是利用低代码平台提高开发效率，满足业务部门的快速应用需求。
平台于 2023 年开始引入，经过需求收集、培训、采购和部署，于 2024 年 4 月 18 日开始正式使用。
目前已使用该平台开发的应用包括：

- 物流运营第三方信息综合看板
- 第三方非生产时间加班审批 (LSP Overtime Approval)
- 供应商绩效考核面板

---

## StoryLine

📅2023-09-07: 收到刘俊邮件，公司考虑推广低代码平台。转发给物流各部门介绍并收集需求。相关邮件和介绍材料: [20230912 - 介绍.pptx](es://20230912%20-%20介绍.pptx) (含视频)

📅2023-09-15: 反馈收集到的需求 Excel 给刘俊，由其转交冯任贵。

📅2023-10-27: IT 组织低代码平台技术分享培训。培训通知: [20231027151808 转发 【报名通知】低代码平台技术分享.msg](es://20231027151808%20转发%20【报名通知】低代码平台技术分享.msg)

📅2023-12-05: 咨询冯任贵，得知平台正在采购中, 预计春节后上线。

📅2023-12-22: TSS 介绍 GPT AI 时提到零代码平台使用简道云方案。咨询李淳确认，当前为测试账号，数据后续会清空。

📅2024-02-26: 咨询冯任贵，得知平台正在部署中, 预计 3 月底完成。

📅2024-04-18: 使用低代码平台设计账号申请收集表, 发送给各科室收集信息，用于后续统一 OA 申请账号。

---

## Reference

### Key Personnel

- [冯任贵](CRM/冯任贵.md) (IT 联系人)
- [刘俊](CRM/刘俊.md) (早期需求收集联系人)
- 李淳 (简道云平台信息确认)

### Related Documents

- [低代码平台介绍与需求收集.msg](es://20230915150911%20低代码平台介绍与需求收集.msg)
- [低代码平台介绍.pptx](es://20230912%20-%20介绍.pptx)
- [【报名通知】低代码平台技术分享.msg](es://20231027151808%20转发%20【报名通知】低代码平台技术分享.msg)

---

## Developed Applications / Sub-Projects

### 物流运营第三方信息综合看板

#### Summary

此看板旨在整合并展示物流运营相关的第三方信息，主要包括人员、设备和车辆数据。

- **项目启动日期:** 2024-04-19 (开发开始)
- **负责人/KU:** 吕志静
- **数据来源:**
  - 厂内第三方人员: 王旭 → 吕志静
  - 运输车辆人员: 刘成羡 → 董建欢
  - 设备信息: 杨文
- **当前状态:** 开发中。计划改进数据展示方式（Overview + 分类报表）并整合更多数据源。

#### Storyline

📅2024-04-19 开始在测试系统进行开发，初期只有模板，无详细数据。
📅2024-05-07 科务会讨论结果：

1. 数据展示改进: 制作 Overview 入口, 分开 人员/设备/车辆 三个报表。
2. 数据源扩展: 增加杨文负责的设备和人员信息；增加刘成羡负责的运输车辆信息。

#### Reference

**参与人：**

- [吕志静](CRM/吕志静.md) (主要负责人/开发者)
- 王旭 (厂内人员数据源)
- 杨文 (设备数据源)
- 董建欢、刘成羡 (运输车辆数据源)

---

### 第三方非生产时间加班审批 (LSP Overtime Approval)

#### Summary

本文档总结了“第三方非生产时间加班审批 (LSP Overtime Approval)”项目的关键信息、当前状态和已知问题。系统利用公司简道云低代码平台，旨在规范和管理第三方人员的非生产时间加班申请与审批流程。

- **应用技术：** 公司简道云低代码平台
- **各厂区上线时间:**
  - MRA1: 2024 年 8 月 1 日
  - MRA2: 2024 年 9 月 11 日 (强制切换时间: 9 月 18 日)
  - MFA & ENG: 2024 年 10 月 21 日
- **项目状态:** 已于 2024 年 11 月 1 日在早会宣布完全上线。
- **最新变更:** 2025 年 6 月 26 日因王晓然经理离职，审批流中 L4 审批人临时调整为 L5 王旭。

**已知问题:**

申请单被拒绝后，提交人没有收到明确的拒绝提示。
王丽霞建议: 复制表单，使用智能助手在流程结束后触发新表单审批流，通过此新审批流发送消息给提交人。
缺陷: 提交人收到的是新审批流信息，无法查看原始审批单历史记录。

#### Storyline

📅2024-07-09 向王旭、潘超展示系统。计划后续进行更广泛展示，并确定 MRA1 为试点厂区。

- [x] 完成: 增加总金额显示。
- [x] 完成: 提交 OA 上线生产环境申请 (LCAG202407004)。
- [x] 完成: 追踪 OA 进度 (于 [2024-07-10] 批准)。

📅2024-07-10 OA 获批。与冯任贵电话讨论，明确需待系统上线后再优化审批流和配置权限。因 IT 的 DB 会议 (预计 7 月 17 日后)，上线时间顺延。

📅2024-07-11 与冯任贵、王丽霞会议。

- [x] 完成: 拆分 L5 和 L4 审批节点。
- [x] 完成: 实现表单联动，自动计算申请单数量。
- [x] 完成: 区分管理员视图/权限 ([2024-07-12] 完成)。

📅2024-07-18 系统成功上线生产环境。

- 待办: 调整导航栏链接。
- 待办: 配置优化邮件提示。

📅2024-07-24 制作培训文档，组织与赵飞的会议。

📅2024-07-29 对 MRA1 业务部门进行系统使用培训。
![Snipaste_2024-07-29_13-55-16](assets/Snipaste_2024-07-29_13-55-16-20240730161528-wmrzagq.png)

📅2024-08-06 发现问题：系统不支持部分附件格式。

- 临时方案: 用户需将文件压缩成 ZIP 后上传。
  ![image](assets/image-20240806163905-j4s5rv3.png)

📅2024-09-11 与 MRA2 部门会议和系统演示。

1. 计划: MRA2 预计 2024 年 9 月 16 日 (周一) 开始使用。
2. 演示内容: 系统登录、主要流程、与旧流程区别。
   ![image](assets/image-20240911105825-my21ugk.png)

📅2024-09-13 刘成羡咨询各厂区上线时间，用于后续结算复审。

📅2024-11-01 在早会正式宣布系统已在所有相关厂区完全上线。
参考资料：[第三方非生加班管理系统 FIN_Stage_Report](es://第三方非生加班管理系统FIN_Stage_Report) (内部链接)。

📅2025-06-26 因王晓然经理离职，通知 IT 王丽霞，将审批流当中 L4 的审批人临时调整为 L5 王旭。这样 L5 王旭审批完成后，因为 L4 位置上也是他，所以审批流会自动通过，继续后续的审批。

#### Reference

- **系统访问链接:** [https://lowcode.bbac.com.cn/login](https://lowcode.bbac.com.cn/login)
- **审批流程图:** [boardmix 在线白板](https://boardmix.cn/app/share/CAE.CLClow0gASoQJszR-G3sLLR3KIWv1m4kcDAGQAE/UQ2laM%EF%BC%8C)
- **旧版本文档 (Sharepoint):** [第三方非生产时间加班审批 LSP Overtime Approval/Sharepoint.第三方非生产时间加班审批 LSP Overtime Approval.md](第三方非生产时间加班审批%20LSP%20Overtime%20Approval/Sharepoint.第三方非生产时间加班审批%20LSP%20Overtime%20Approval.md)
- **相关 OA 审批:** [LCAG202407004](https://oa.bbac.local/km/review/km_review_main/kmReviewMain.do?method=view&fdId=1909692470b54a2be775ebc4c378eca5)
- **阶段性报告:** [第三方非生加班管理系统 FIN_Stage_Report](es://第三方非生加班管理系统FIN_Stage_Report) (内部链接)
- **数据网盘:** 地址可能变更，请使用者自行确认。

**参与人：**

- 冯任贵 (IT)
- 王丽霞 (IT - 简道云技术支持)
- 王旭、潘超 (早期需求/评审)
- 赵飞 (MRA1 培训协调?)
- 刘成羡 (结算复审/信息咨询)
- 王晓然 (前 L4 审批人，已离职)

---

### 供应商绩效考核面板

#### Summary

本项目旨在创建供应商绩效考核的标准化、数字化面板，以便于追踪和可视化。

- **触发背景:** 杨文在部务会展示后，于丹提出任务要求 (2024-08-15)。
- **核心目标:** 自动化收集来自多个系统 (IPT, AmSupply, MRS, iPortal, EDW, ATM) 的数据，用于评估物流服务提供商 (LSP) 的绩效，取代或简化当前的报告流程。
- **主要内容:** 涉及定义业务需求 (BRD)，与 IT 部门 (刘俊) 和相关系统负责人 (梅军) 沟通数据获取方案，明确具体所需数据指标 (师帅负责细化)。
- **当前状态:** 项目已启动，BRD (BRD00405) 初稿已完成，正在与 IT 讨论技术实现方案 (可能参考数据中台或 RPA)，数据源的具体细节待明确。项目已被纳入 E&M Strategy Workshop。

#### Business Requirements Document (BRD00405) Details

**1. Description (描述)**  
需求是提供一个数据自动化导出的方案, 用于收集来自不同系统的数据，涉及到的系统包括：IPT, AmSupply, MRS, iPortal、Central EDW、ATM。这些数据之后将用于评估物流服务提供商的表现。例如卡车等待时间、系统外线供料、停线时间、库存准确率。

**2. Economical statement (经济性)**  
通过自动化的从多个系统获取数据, 将简化绩效考核流程，减少手动错误，并提高绩效评估的准确性和及时性。有利于效率的提高和管理层决策制定，最终提高运营效率和节省成本。

**3. Acceptance criteria (验收标准)**

- 能够自动从所有指定系统获取数据，无需手动干预。
- 具有长期存储数据和备份数据的功能。

**4. Demand Constraints (约束)**

- 遵循流程: 北京奔驰第三方物流服务商绩效管理流程 (S8000935P)
- 依赖关系: 可能依赖其他项目、流程或产品 (具体待明确)。

**5. Affected Application(s) (受影响系统)**  
IPT, AmSupply, MRS, iPortal, EDW, ATM

**6. Volume (数据量与用户)**

- **受影响用户数量:** 服务于所有参与 LSP 绩效评估的相关人员，包括各物料管理科、第三方管理科。
- **使用频率:** 根据所连接的系统，理想情况是每日更新数据供绩效核算。
- **数据量:** 根据审计要求，预计需保存 18 个月的数据。长周期数据可考虑压缩存储。

---

#### Storyline

📅2024-08-15 项目启动。杨文在部务会展示后，于丹提出任务，要求将 LSP 绩效考核标准化、数字化，便于追踪和可视化。参考邮件：`转发_ Meeting Minutes _ CW32 - Bi-weekly Logistics Operation Management Meeting .eml`。首要任务是编写 BRD，明确数据获取需求。
📅2024-08-19 与师帅会议。讨论当前流程：管理科每日/周发送 KPI 报告，月度汇总给杨文组出报告。师帅将负责细化具体所需数据/材料，并与潘超一同向杨文获取现有报告。
📅2024-08-21 与刘俊进行 BRD 会议。IT 团队将内部讨论技术方案，考虑是否能参考柳清溪的数据中台方式提供数据。
📅2024-09-30 与刘俊会议，并与 iPortal 管理员梅军通话。讨论要点：

1. 需要明确用户所需数据的具体来源。
2. iPortal 的一般查看功能无需账号，使用 RPA 抓取数据在技术上可行。

📅2024-11-22 本项目被选为三个关键项目之一，需要向 E2 级别的 Steffen 进行汇报解释。

#### Reference

- **启动背景邮件:** `转发_ Meeting Minutes _ CW32 - Bi-weekly Logistics Operation Management Meeting .eml` (请确认文件可访问性或提供更稳定链接)
- **业务需求文档 ID:** BRD00405
- **相关流程文件:** 北京奔驰第三方物流服务商绩效管理流程 (S8000935P)
- **相关项目/活动:** [E&M Strategy Workshop](E&M%20Strategy%20Workshop.md)

**参与人：**

- 于丹 (任务提出者)
- 杨文 (业务专家/报告提供者)
- 师帅 (数据需求细化/协调)
- 潘超 (协助师帅)
- 刘俊 (IT 接口人/BRD 审核)
- 梅军 (iPortal 系统管理员)
- Steffen (E2 管理层/汇报对象)
- 柳清溪 (其数据中台方案被提及作为参考)

**受影响系统：**

- IPT
- AmSupply
- MRS
- iPortal
- Central EDW (EDW)
- ATM

---

## 待确认事项

- 参与人身份确认: 以下几位参与人可能指向同一个人，请确认：
  - 组 1: "冯任贵", "冯任贵"

