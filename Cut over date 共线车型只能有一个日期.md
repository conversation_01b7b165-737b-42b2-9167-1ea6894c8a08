﻿---
title: "Cut over date 共线车型只能有一个日期"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# Cut over date 共线车型只能有一个日期

## Summary

AmS 相比 IPT 来说, 会自动根据车辆的真车需求来设置 Cutover Date。但其基础逻辑设置为同一生产线只会有一个 Cutover Date，因此造成共线生产车型需要等待所有车型的真车需求都到位后才会调整 Cutover Date。

- **原始方案**: 通过 COC 沟通, COC 在 ASF 中提供了单独设置 Cutover Date 的解决方案。 [Issue-Check: 时效性冲突]
- **遗留问题**: 具体工作流程安排, 原计划由 MRP 同 Production Planning 自行讨论, WP2 不再参与。 [Issue-Check: 时效性冲突]

**最终状态**: 根据 StoryLine 最新记录(2021-10-26)，目前已**恢复由生产计划手工控制的模式**来解决此问题，原先讨论的 IT 解决方案可能已被取代。

## StoryLine

📅2021-10-26: 恢复由生产计划手工控制的模式。
📅2021-10-03: 问题导致 243 车型的预测需求与真车 cutover date 相差 3 个月以上，造成 243 车型无需求。
📅2021-07-13: 根据李明奎的答复, 反馈给 MRP：具体情况再联系杨晟浩进行系统调整。
📅2021-06-19: Email 杨晟浩, 咨询业务方面的处理方案。
📅2021-02-02: 会议中 COC 提供了解决方案，从 IT 端关闭此议题，转由业务部门继续讨论工作流程。
📅2021-01-15: 同李鸿雁沟通后, 她也通知 WP2 人员将此问题放入 fit-gap 清单中。
📅2021-01-07: 找到实例, 要求 WP2 放入 fit-gap 清单。
📅2020-12-09: 从董硕处首次听说此问题，由李明奎详细解释。核心是单一生产线只有一个 Cutover date, AmS 会自动选择 full capacity 的日期作为 Cutover date。根据 Florian 的解释，会选择靠前的日期，因此可能导致一部分真车需求无法完整显示。

## Reference

**核心人员**: [董硕](CRM/董硕%20Unity.md), [李明奎](CRM/李明奎.md), [Florian](CRM/Florian%20Schneider.md), 杨晟浩, 李鸿雁

**相关文档**:

- [20210202 会议展示 PPT](assets/20210209161420-y74boc2-WP2_fit-gap%20clarification_cut%20over%20date%20issue_20210202.pptx)
- [答复 Cutoverdateexample.eml](assets/20210107094247-wc039ic-答复%20Cutover%20date%20example.eml)
- [回复 AMS project cut over date issue... .msg](<es://%20回复%20AMS%20project%20cut%20over%20date%20issue%20in%20model%20mix%20per%20production%20line%20(3rd%20discussion)face%20by%20face%20show%20system%20operation.msg>)

