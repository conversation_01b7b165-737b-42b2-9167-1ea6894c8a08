﻿---
title: "Training.操作文档 - SM37导出call off记录查看release结果"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 操作文档 - SM37 导出 call off 记录查看 release 结果 (Duplicated 2024-07-03 09:19:11)

> 通过下载后台 call off 的 JOB 记录，查看是否有零件因为 SA 占用等原因没有成功发送 call off，以便补发

# 操作步骤

1. ​`SM37`​ ->User name: `发送Call Off的MRP用户名`​-> 点击 Execute 执行  
   或是在 JOB Name 中填写`RM06EFLB`​-> 点击 Execute 执行

![image.png](assets/image-20211029093659-2ahj4yk.png)

2. 选中 job(用时多的)-> 单击"Spool"

![image.png](assets/image-20211029093717-35iyy03.png)

3. 点击如下

![image.png](assets/image-20211029093725-aeh22go.png)

4. 初次使用时，需要先点击“settings"-> 将页数改为 2000 或是更大的数值以便完整显示

![image.png](assets/image-20211029093732-tw9zvzz.png)

5. 点击保存文件将文件保存到本地

![image.png](assets/image-20211029093737-bw5e7mi.png)

6. 打开文件，选中"R"列-> 点击“Filter"-> 查看过滤出来有没有非”10“的类型，如果有的话，查看是哪个零件，具体情况具体分析，可能重新发送一下 call off 即可。

![image.png](assets/image-20211029093745-1rv1qii.png)

# 文档信息

> 李妍妍 (<EMAIL>)
>
> 李天宇（<EMAIL>)
>
> 初次发布时间：2021-10-28
>
> 最后更新时间：2023-07-18
>
> 版本: V1.1

‍

