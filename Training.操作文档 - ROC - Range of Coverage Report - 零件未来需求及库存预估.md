﻿---
title: "Training.操作文档 - ROC - Range of Coverage Report - 零件未来需求及库存预估"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 操作文档 - ROC - Range of Coverage Report - 零件未来需求及库存预估

> 通过 ROC Report，可以大批量的导出零件在未来指定日期的系统库存及之后数天的系统消耗数据。
>
> 该功能可方便的用于零件 EOP 控制以及长假期后零件库存水平的预估等工作。

# 操作步骤

AMS T-Code：`/DA0/0080_ROC_STOCK`​

IPT T-Code：`/DFJ/10B_ROC_STOCK`​

## 筛选界面

![image](assets/image-20230206111452-wdglav9.png)

第一部分用于 MRP Area 筛选，需要查看 Plant Level （104）的数据时请选择第一行后填入 `104`​，其他情况请选择第二行的 MRP-Area 后填入对应的 MRP Area。

请注意这里不能填入 `*` ​ 作为通配符，需要填入具体的 MRP Area。

第二部分用于数据范围选择，除了常见的 Material、Controller 等字段外，还请特别注意 Date 和 Demand qty for newxt N workdays 两个字段。

其中 Date 用于指定包含今天在内的未来某一天的日期，之后的运行结果将计算该日期的数据。

而 Demand qty for newxt N workdays 用于指定在之后的结果页面中，汇总显示在上述日期后的 N 个有需求的日期内的需求总和。例如上图 Date 指定为 2 月 6 日，如果 N 填入 3，则在之后的结果界面中，将会汇总显示 2 月 7 日至 9 日的需求数值（假设这 3 天均有系统需求）。

第三部分勾选 Consider Safety Stock 后，将会在计算结果的 Stock 中考虑 MM02 中设置的 Safety Stock 数值。

### 结果解读

如下例填入筛选字段。为测试需要，零件设置了 safety stock=1。

![image](assets/image-20230206112858-tzxb7v5.png)

如下结果同 MD04 对比，可见在 2 月 10 日上，库存、DOH、Demand 都是完全对应的，Demand N 为我们上面填入的 `3` ​ 天，汇总了未来 3 个有需求的日期的需求值。

![image](assets/image-20230206113247-k4p4ftv.png)

如果我们取消掉 Consider Safety Stock 的勾选，则会在 Avail. qty 中反推掉 safety stock 的影响，可看到数值加回了 1pc

### ![image](assets/image-20230206113546-6jaqpsb.png)

# 常见问题解答

1. ROC 的结果与 MD0**4** 数值高度一致，也就是说在 MD04 中看到的零件 Purchase Requisition 和 TrnRes 等非实际订单、在途的数字也会被统计在内。在使用时还请注意需要排除这方面的影响。

# 文档信息

> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>
> 初次发布时间：2023-02-06
>
> 最后更新时间：2023-02-06
>
> 版本: V1.0

