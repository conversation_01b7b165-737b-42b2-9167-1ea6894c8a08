﻿---
title: "Tableau & PBI"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# Tableau & PBI

‍

PBI的Local IT管理员: [夏绍军](CRM/韦围/夏绍军.md)

# Tableau切换Power BI(PBI)

## ⭐️ Key

[📅20221205 收到IT消息，Tableau将在2023年切换到Power BI，现收集Tableau使用情况。202212...](#20221206100755-j911yh1)  
文件统一存放在[Tableau to Power BI 20221209](es://Tableau%20to%20Power%20BI%2020221209)

## 📆 Storyline

📅20221205 收到IT消息，Tableau将在2023年切换到Power BI，现收集Tableau使用情况。[20221206 转发 收集Power BI需求及Tableau迁移需求_20221206_100847.zip](es://20221206%20转发%20收集Power%20BI需求及Tableau迁移需求_20221206_100847.zip)

📅20221206 emailed users收集信息。

📅20221209 反馈用户信息给刘俊。

📅20230103 IT要求区分统计创建和view权限人数。 

> 此前反馈11人均为creator。
>
> View权限需要添加所有E3,E4,E5，且每个E5内增加一个viewer，排重后总共54人。
>
> 考虑到和AD绑定，不知道能否通过账号密码登录？如果是和电脑绑定的，那么11个Creator还需要再有约8个backup

是和AD绑定的  
​![image](assets/image-20230619135519-juvmdvb.png)

📅20230208 [转发 Power BI试用资源与Tableau证书优化-20230207.msg](es://转发%20Power%20BI试用资源与Tableau证书优化-20230207.msg)，准备进行为期一个月的测试。需要二次核实迁移的Tableau报告。Tableau明年将继续使用但是会有严格的成本分析。

📅20230321 会议，IT同事介绍说会有约半年的并行期，且会提供PBI的技术支持，且物流报表比较简单，确认用户届时自己进行迁移工作。

📅20230619 IT开始收集creator的名字，需要BRD签字文件。

![image](assets/image-20230807090921-aqh5f93.png)

📅20230918 第一轮培训结束。

## 🔗 Reference

[夏绍军](CRM/韦围/夏绍军.md) [陈虎](CRM/陈虎.md)

[刘俊](CRM/刘俊.md)

‍

# PBI工作区申请方法

将报表发布到高级工作区后，可以将具有BBAC邮箱的用户添加成工作区的查看者，相应用户即可访问并查看您所创建的报表。

> Power BI申请工作区所需信息，需要填写下方信息，发送 夏绍军 和 陈虎（第三方）
>
> 所需信息：
>
> 工作区名称：部门+用途简称  
> 业务管理员：  
> 用途：

‍

# 自动刷新数据

由于当前业务大多数数据都是本地数据（本地数据库/本地平面文件）。如果需要已经发布的PBI报表/数据集/数据流能够自动刷新本地数据，需要在本地安装数据网关。

如果您需要安装数据网关，请留意以下内容：

1. 原则上每个三级部门可以创建一个数据网关，如果业务部门之间能够达成一致，也可以多个部门共用一个网关
2. 数据网关需要安装在能够保持开机的工作站或者服务器上,且每个服务器只能装一个网关
3. 工作站或服务器需满足以下配置：

 a)      至少8核以上的CPU

 b)      至少4G以上的可用C盘空间

 c)      至少8G以上的可用内存

 d)      能够运行.Net Framework 4.8以上版本

 以上配置会影响运行效率，如果您对效率要求较高，可考虑使用更大的资源或后期进行扩容

4. 准备好服务器资源后，请将AD账户PBIGW添加到服务器管理员组当中，确保PBI应用管理员能够远程访问到您的服务器资源
5. 联系PBI应用管理员，提供服务器IP，网关业务管理员邮箱。PBI管理员将远程为您安装配置网关，过程当中有可能需要重启，请您保持与我们的联系以便随时沟通解决突发问题。

