﻿---
title: "Training.逻辑介绍 - 二次收货时间差导致负库存"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 逻辑介绍 - 二次收货时间差导致负库存

Hayo Analysis

> Furthermore IDOCs with message type /DA0/0150_WMMBXY_ABG are created to "copy" this GR to ORI-delivery. In the ORI-delivery we also have the load carrier for which we need to created material documents.
>
> Sometimes it happens that such material document cannot be created due to locks in material master. Then the IDOC cannot be processed and it goes to status 51, in todays GR process for delivery 24200432
>
> we faced this issue with IDOC 23576007. So in this case we have already created the 942-material document but due to the fact that the IDOC could not be processed successfully we have no 101-material document
>
> at this poin of time. We have IDOC-reprocessing jobs running every 30 minutes. Normally, and also in case of he mentioned ID<PERSON>, those IDOCs are processed successfully with next job running.
>
> This is the reason why we have time differences between creation of 101-material document and 942-material document.
>
> Example IDOC: 23576007
>
> Furthermore those IDOCs are serialized. So if one IDOC goes to status 51 (like above) then following IDOC go to status 66. Regarding to delivery 24200432  we can observe this behaviour in IDOC 23576127.
>
> Regarding to creation of 101-material documents we have the same consequences like with the IDOC 23576007. Also those IDOCs are reprocessed by job.
>
> Example IDOC: 23576127
>
> So from our point of view this is normal system behaviour because of 2GR logic with special china-functionality with load carriers. Negative stock would be equalized when IDOCs are reprocessed successfully.

---

checked with Kexin

usually after 942 will 101.

But today it has a 30 min gap for A 247 830 28 00.

Caused a negative stock in W440 area.

![image.png](assets/image-20211029140926-damw863.png)

‍

