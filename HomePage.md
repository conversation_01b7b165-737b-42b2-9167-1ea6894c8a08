﻿---
title: "HomePage"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# HomePage

‍

```
SELECT * FROM blocks
WHERE type = 'd' 
AND hpath NOT LIKE '%CRM%'
AND updated < '2022-01-01'
AND box is '20210811083548-6u1vyoc'
ORDER BY updated
LIMIT 5
```

>> 📅20230110 目前254使用PFM系统的生产计划应由生产计划科肖凯通知装焊车间董照龙，由后者码放进入系统。
>>
>
>> 本例中后者码放SOP需求在1月份，实际生产是在2月份。 [答复 V254 sop 时间_20230110_111616.zip](es://答复%20V254%20sop%20时间_20230110_111616.zip)
>>

> ![image](assets/image-20220809141417-nts035t.png)
>
>> AMS的零件主数据中有三种Safety Time供用户选择使用，针对这三者的逻辑与关系进行介绍。
>>
>
> # 💻逻辑描述
>
> ## Safety Time
>
> Safety Time是最基本的安全天数形式，在AMS中已经基本弃用，但其逻辑仍是理解后续两项的基础。
>
> 其直译为安全时间，体现**订单比需求日期的提前量**。这一提前量是按照**工作时间**（工厂日历）进行计算的。
>
> 我们假设这样一种情况，系统设置`Safety Time = 1`，且`Rounding Value = 1`
>
> ![image](assets/image-20220809143737-4hw4v88.png)
>
> 请看这里订单数（Receipts）均比需求数量（Plnd ind.)提前一天，且12月31日至1月2日间没有生成订单，因为这期间是非工作日。
>
> ## Dynamic Safety Time
>
> 在Safety Time的基础上，AmS中新增了Dynamic Safety Time的参数，将参数从整数精确到了小数点后一位，例如`2.5`或是`11.5`等
>
> ## Lot Safety Time
>
> 该参数特别针对试装车辆需求进行订单提前期控制。试装需求在MD04中一般以B作为结尾。
>
> # 🧾简单举例
>
> 系统参数配置如下：
>
> ![](assets/clip_image001-20210618144950-dusbkl0.jpg)
>
> 可以看到8号生成了1条schedule line, 为试装订件。 序列化需求的schedule line生成在17号。分别提前了15天和7天（工作日）。
>
> ![](assets/clip_image002-20210618144950-75kvhv7.jpg)
>
> 如果库存够, 则不会再生成额外的schedule line。
>
> ![](assets/clip_image003-20210618144950-flug6mh.jpg)
>
> # 📈进阶讲解
>
> ## Safety Time ≠ 最低库存天数
>
> 我们在上文的基本逻辑介绍中提到过Safety Time[其直译为安全时间，体现订单比需求日期的提前量。这一提前量是按照工作时间（工厂日历）进行计算的。](Projects/AmS@BDA/AMS.BDA.Training/逻辑介绍%20-%20三种Safety%20Time（Normal.Dynamic.Lots）.md#20220809141651-xrtgaq8)请注意其描述为**提前量**，和库存没有直接的关系。
>
> 我们假设这样一种情况，系统设置`Safety Time = 1`，且`Rounding Value = 1`
>
> ![image](assets/image-20220809145343-0tuqbil.png)
>
> 可以看到从1月6日开始，直到31日订购506pcs给2月1日使用前，可用库存为0。这是因为1月31日是工作日，系统只会把2月1日的订单提前一个工作日到1月31日，而不会去确保1月6日的库存不低于1天。
> 因此<u>要特别注意工作日与实际生产日期不一致的情况。</u>
>
> # 📰文档信息
>
>> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>>
>> 初次发布时间：2021-06-18
>>
>> 最后更新时间：2022-08-09
>>
>> 版本: V1.2
>>

> # 为 MRP 更新轴瓦使用率计算 VBA 为 Tableu
>
> 基本逻辑
>
> ```mermaid
> graph LR
>   手工调账数量 --> 每周用量比例 --> 移动平均值&最大值
> ```
>
> 给轴瓦分组，相同前缀的为一组
>
> # 数据获取
>
> ## 每日需求
>
> SQ01-MRP IT-DEPREQ，放入主号
>
> Run 16 秒出结果, 速度很快
>
> ## 手工调账记录
>
> MB51-309
>
> Run 4 秒出结果, 速度很快.
>
> ## 文件归档
>
> `工具改为 Tableau-增加 V254 轴瓦零件号 - 20210702.zip`

> [AmS.BDA.Gaps.Missing DepReq in MD04](Projects/AmS@BDA/Gaps%20&%20Issues/更改需求显示逻辑回到IPT模式/AmS.BDA.Gaps.Missing%20DepReq%20in%20MD04.md)
>
> [AmS.MD04 OrdRes 汇总显示](Projects/AmS@BDA/蓝图%20&%20流程/AmS%20BDA%20T-Code%20List/MD04/AmS.MD04%20OrdRes%20汇总显示.md)
>
> [逻辑介绍 - MD04需求拆分显示](Projects/AmS@BDA/AMS.BDA.Training/AmS%20系统需求显示及计算逻辑/逻辑介绍%20-%20MD04需求拆分显示.md)
>
> [逻辑介绍 - MD04 - End Replenishment Lead Time](Projects/AmS@BDA/AMS.BDA.Training/逻辑介绍%20-%20MD04%20-%20End%20Replenishment%20Lead%20Time.md)
>
> # Selection Rule
>
> ![image.png](assets/image-20211112103309-axgk8bt.png)
>
> 📅20211008
> 需要张嘉描述, 然后发给 [Roland](CRM/Roland%20Herold.md)
>
> 预计 11 月更新
>
> 📅20211103 系统已传入, close topic
>
> 📅20221017 AMS@MRA上线后，梳理了系统中所有的rules，并邮件通知MRP是否需要修改，提醒其之后修改就得走大流程了。[20221026 MD04 Selection Rule_20221026_043444.zip](es://20221026%20MD04%20Selection%20Rule_20221026_043444.zip)
>
> 📅20221110 [禄魁](CRM/禄魁.md)提出需求要对104的零件增加rules，反馈其需要BRD后一直未答复。[20221201 RE MD04 selection rule.msg](es://20221201%20RE%20MD04%20selection%20rule.msg)
>
> # Change Navigation in MD04
>
> ![image.jpg](assets/image-20210708135906-km3iqpd.jpg)
>
> ![image.jpg](assets/image-20210708135917-vzrzb65.jpg)

> # ref type indicator
>
> 有如下 4 种 referece type:
>
> ![image.png](assets/image-20210821152939-pjdmjiy.png)
>
>> H: Inhouse part ( 在 BDA 就是指 press parts(press plant), engine parts (engine plant), battery parts (battery plant)
>>
>
>> L: Vendor part (外购件）需要 Globus contract
>>
>
>> W: plant delivery part (在 BDA 就是指 BDA 和顺义；但是项目并行期间，MFA 上线后，其他未上线地工厂都是 W-Malte 已经设置）不需要 globus contract
>>
>
> Table [/DA0/4010_MMI_KB](Projects/AmS@BDA/蓝图%20&%20流程/AmS%20BDA%20T-Code%20List.md#20210309174016-zompbs9)
>
> ![image.png](assets/image-20210821153004-wj5jkgy.png)

‍

‍

‍

