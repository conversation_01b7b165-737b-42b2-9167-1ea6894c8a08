﻿---
title: "Buildable Report Manual"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# Manual

# 鏁版嵁鍑嗗

## 鏁版嵁涓嬭浇

> 绯荤粺 = `PSW`鈥?>
> T-Code = `SM37`鈥?>
> 涓嬭浇淇濆瓨涓篢XT鏂囦欢.涓嬭浇鏂瑰紡鍙傝€僛鎿嶄綔鏂囨。 - 閫氳繃鍚庡彴杩愯鍙婂鍑虹郴缁熸姤琛╛SM37](Projects/AmS@BDA/AMS.BDA.Training/鎿嶄綔鏂囨。%20-%20閫氳繃鍚庡彴杩愯鍙婂鍑虹郴缁熸姤琛╛SM37.md)

1. 搴撳瓨鏁版嵁

    1. Job Name: `RPA - STOCK LX03`鈥?    2. User Name: `*`鈥?    3. ![image](assets/image-20240703090956-acy92xx.png)
2. ASN鏁版嵁

    1. Job Name: `AQA0WP4=========WP4_CON_BLGBGR`鈥?    2. User Name: `*`鈥?    3. ![image](assets/image-20240703091315-4rkpirc.png)
3. 闆朵欢娓呭崟 = `Material_List.xlsx`鈥?
## 鍏朵粬鏁版嵁

浜ч噺鏁版嵁: 鐩存帴缁存姢鍒癕asterSheet.xlsx鐨凱roduction椤典笅闈?
![image](assets/image-20240703091516-to74xx0.png)鈥?
# 鏁版嵁澶勭悊

## 鎿嶄綔

鎵撳紑`MasterSheet.xlsx`鈥? 鐐瑰嚮`鏌ヨ鍜岃繛鎺鈥?
![image](assets/image-20240703092345-ou2zv5m.png)

鍙充晶鍥涗釜鏌ヨ, 鍒嗗埆瀵瑰簲鎴戜滑涔嬪墠鐨刐鏁版嵁鍑嗗](#20240703084259-v4muti3).

![image](assets/image-20240703093942-yauike6.png)

鍒濇浣跨敤鏃堕渶瑕佹寚瀹歍XT鏁版嵁鐨勪綅缃?

鍚庣画浣跨敤鐨勬椂鍊欐妸鏂扮殑TXT鏂囦欢鍚屽悕鏀惧埌鍚屾牱鐨勪綅缃?

### 鎸囧畾TXT鏁版嵁鐨勪綅缃?鍒濇浣跨敤)

1. 鍙屽嚮鏌ヨ鏉＄洰, 姣斿![image](assets/image-20240703093748-km1sltf.png)
2. 浠ュ簱瀛樻暟鎹负渚? 濡傚浘渚濇鐐瑰嚮鏉ョ‘璁ゆ暟鎹枃浠剁殑浣嶇疆

![image](assets/image-20240703094018-ou5gn9f.png)

鏇存柊瀹宍Stock`鈥? `ASN`鈥嬪拰`Result`鈥嬬殑鏁版嵁婧愬悗, 鐐瑰嚮`鍏抽棴骞朵笂杞絗鈥?
![image](assets/image-20240703094118-8ug7u7p.png)

### 鍒锋柊鏁版嵁(鍚庣画浣跨敤)

鍦‥xcel涓€夋嫨`鍏ㄩ儴鍒锋柊`鈥?
![image](assets/image-20240703094314-ffy2xm2.png)

澶ф鍑犵閽熷悗鏁版嵁灏变細鍒锋柊瀹屾垚鍙互浣跨敤.

## 鍚庡彴澶勭悊閫昏緫(閫夎)

TBD

## 鏇存柊DataForPBI

鎵撳紑`DataForPBI.xlsx`鈥?
涓ユ牸鎸夌収鏍煎紡鏇存柊鍚勮溅鍨嬬殑鍚勬湀浜ч噺鍜屽簱瀛樹俊鎭?

# PowerBI鎶ヨ〃

鎵撳紑`Stock Report Critical stock level.pbix`鈥?
### 鎸囧畾鏁版嵁鐨勪綅缃?鍒濇浣跨敤)

鍦ㄦ暟鎹噷鎵惧埌Sheet1, 鐐瑰嚮鍙抽敭, 閫夋嫨`缂栬緫鏌ヨ`鈥?
![image](assets/image-20240703095321-8oks7hc.png)

鍙傝€冧笂闈㈢殑鏂规硶鏇存柊鏁版嵁婧?

### 鍒锋柊鏁版嵁(鍚庣画浣跨敤)

![image](assets/image-20240703095419-fusxwra.png)

鈥?

