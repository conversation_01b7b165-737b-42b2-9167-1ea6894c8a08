---
title: "NTP.eATS"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "NTP"

category: "Project"
project: "NTP项目"
---
# eATS 项目

## eATS 项目

### Summary

eATS 项目当前聚焦于 ET1 的准备工作与问题解决。ET1 的首次 Call-off 已于 6 月初完成，但物料清单（BOM）尚未完全就绪，因此在未来两周将采用手动 Call-off 的临时方案，预计首次 GR 在 10 月中旬。为支持此流程，仓库定制将在 7 月底完成，并可使用临时的电池厂卸货点。与此同时，项目正处理混合 HU（Handling Unit）的三个核心问题，其中自动分拣和标签打印问题已由本地 IT 支持解决。Test Part 1 的测试准备工作正在进行，其中关键的 ULP 设置问题已取得进展，为接下来的集成测试铺平了道路。关于 ET1 的线边供应流程，已确认将采用标准的系列流程。QM 相关的解决方案文档已完成展示，目前处于签核流程中。

### StoryLine

📅2023-10-31: 李宁咨询系统和 Storage Location 的事情, 安排会议。

📅2023-11-01: 生产线与厂房均为新建。

📅2024-10-14: 朱梦鑫邀请会议讨论序列化零件订购事宜。由于德国项目已临近 SOP，德方不愿再接受 EX33 订单。李宁尝试推动临时方案以赶上 ET 和 OT2。

📅2024-11-08: kickoff meeting。

📅2024-12-06: 与 MRP 讨论 buy sell 流程，MRP(张嘉)主要诉求是参考 FBAC 模式。

📅2025-03-10: Andreas Nellessen 提议将 ET1 准备就绪检查纳入每周 eATS 会议。

📅2025-04-01: Tobias Wendel 和 Georg Völker 参加会议，评估 ET1 提前至 2025-10-15 的可能性。

📅2025-05-07: 李宁邮件提出 eATS 项目的仓储需求：临时方案使用 HAF1 匹配 MRP_104 区域，未来 eATS 主线建立后，需要为 MRP_eATS 分配特定的 SL 和 ULP。

📅2025-05-08: Aysun Sanli-Erler 邮件回复，建议新定制 ULP=EI11X, GR Zone=12R, Sloc=UAFL, MRP Area=104_EN1。

📅2025-05-27: Han Yeyun (韩叶云) 确认 ET1 (Large eATS) 不会提前到 2025-10-15。

📅2025-06-03: 完成首次 ET1 Call Off。

📅2025-06-13: 进口组 MRP 李丹丹和张嘉咨询 eATS 总成号 A1743403200 & A5403401800 的维护问题。

📅2025-06-16: 维护中新增 ZB 编号：MMA/compact 对应 A1743403200，MB.EA-M/large 对应 A5403401800。

📅2025-06-25: Georg Völker 宣布 CoC 决定：BDA eATS 的 GoLive 将在 2025 年 11 月 4 日（周二）ET1 进行。进口 call-off 需由业务部门从 2025 年 8 月 4 日开始执行。

📅2025-07-01: Christoph Muench 邮件通知，用于 eATS 组件的 ULP 606X 和 607X 已在 KSV 系统中维护，可用于下周针对 Benteler 的集成测试。CoC 内部将进行预测试。

### Reference

- 核心人员:

  - Tim Bawolski (Logistic PL)
  - 李宁 (LCM)
  - 李丹丹 (MRP)
  - 张嘉 (MRP)
  - 孙雨朦 (MRP)
  - 贾尚 (MRP)
  - Tobias Wendel (IT-PM)
  - Han Yeyun (Production Planning)
  - Christoph Muench (WP3)
  - Andreas Nellessen
  - Georg Völker
  - Mark-Oliver
  - Aysun Sanli-Erler
  - Helmut
  - Wen Juan (物流运营)

- 相关文档:

  - [In-house eATS sell to supplier](In-house%20eATS%20sell%20to%20supplier.md)
  - meeting minutes - EB5@BBAC - IT Biweekly Meeting, Tue 27.05.2025

- 关键里程碑:
  - 📅2024-11-08: kickoff meeting
  - 📅2025-11-04: BDA eATS GoLive (ET1)
  - 📅2025-11-24: ET1 (Compact eATS) POR

---

## 1st call off for ET1

### Summary

ET1 的首次 Call-off 已于 2025 年 6 月 3 日完成。然而，由于 eATS MMA 的物料清单（BOM）尚未就绪，项目方决定采用临时手动 Call-off 的方式处理未来两周的进口零件需求。预计首次收货（GR）将在 10 月中旬完成。为支持此流程，相关的仓库（WH）定制预计在 2025 年 7 月底于 PSV 系统上线。同时，项目可以利用电池厂现有的临时卸货点，但系列的卸货点需要在 AmSupply 系统上线前创建完成。

### StoryLine

📅2025-06-03: 为 ET1 (eATS MMA) 完成首次 Call-off。

📅2025-06-10: Tim Bawolski 邮件询问 Andreas Nellessen 关于在 EP1 系统中生成需求和执行 Call-off 的时间点，并提到听说下周即可准备就绪。

📅2025-06-10: Christoph Muench (WP3) 在周会上回复，eATS MMA 的物料清单（BOM）尚未进入 P 系统，但在 K 系统中已找到（但无 SDSA，无需求）。

📅2025-06-10: Tim Bawolski 提出临时方案：在接下来的两周内，ET1 的进口零件将通过手动方式进行 Call-off，预计首次收货（GR）在 10 月中旬。

📅2025-06-10: Helmut 确认，仓库（WH）的定制将于 2025 年 7 月底在 PSV 系统中可用，因此 10 月份的收货是可行的。

📅2025-06-10: Aysun Sanli-Erler 确认，现在可以使用电池厂的临时卸货点。系列的卸货点尚未创建，需要在 AmSupply 上线前完成。MRP 计划员可以为手动 Call-off 的零件切换卸货点。

📅2025-06-30: ET Call-off 的集成测试正在准备中，目标是为 2025 年 8 月 4 日的首次进口 Call-off 交付。

📅2025-06-30: WP3 正在测试爬坡阶段的卸货点（EI11X）。

### Reference

- 核心人员:

  - Tim Bawolski
  - Andreas Nellessen
  - Christoph Muench
  - Helmut
  - Aysun Sanli-Erler

- 相关系统: EP1, PSV, AmSupply

---

## Mixed HU Handling Issues

### Summary

项目在混合 HU（Handling Unit）处理上遇到三个主要问题：1. 混合 HU 无法被自动分拣；2. HU 标签打印错误；3. 首批货物存在质量问题。针对前两个问题，本地 IT 团队已提出解决方案并等待业务部门反馈。对于质量问题，仍在等待研发部门（RD）的输入。临时解决方案是在项目正式上线前，手动将 eATS 零件重新包装到新的 HU 中。

### StoryLine

📅2025-06-16: 决定在项目上线前，采用临时解决方案，即手动将 eATS 零件重新包装到新的 HU 中，以应对混合 HU 的处理问题。

📅2025-06-16: Wen Juan 报告，由于 eATS 仍处于项目阶段，HU 标签无法在 SP01 系统中打印。下一步将与 WP1 单独讨论。

📅2025-06-23: 本地 IT 团队已针对“混合 HU 无法自动分拣”和“HU 标签打印错误”两个问题提供了支持，并发送了操作指令，正在等待业务部门的反馈。

📅2025-06-23: 关于首批货物的质量问题，项目团队正在等待研发部门（RD）的输入。

📅2025-06-30: 问题 1 和问题 2 已由本地 IT 支持解决。问题 3 尚无更新。

### Reference

- 核心人员:

  - Wen Juan

- 涉及部门: 本地 IT, WP1, RD (研发部门)

---

## Test Cases for Test Part 1 (01.07-07.08)

### Summary

Test Part 1 的测试用例正在收集中，各工作包（WP）的测试准备工作在进行中。WP2、WP3、WP4 的 Dry Try 正在进行。针对 WP3 的 ULP（卸货点）设置问题，相关 eATS 组件的 ULP 已在 KSV 系统中维护，为下周的 Benteler call-off 集成测试做准备。WP5 的测试依赖 WP1 的 HU 创建，WP6 的测试计划需与 MBFS 进一步对齐。

### StoryLine

| WPs | Test Scenario | Status -
| WP1 | Test case in check | 📅2025-06-30: in planning. -
| WP2 | I01 Call-off (Benteler full scenario)<br>I02 ZF Interface verification | 📅2025-06-30: Dry Try in progress-
| WP3 | I01 Call-off (Benteler full scenario) | 📅2025-07-01: Christoph Muench 确认 eATS 组件的 ULP (606X, 607X) 已在 KSV 系统中维护，可用于下周的 Benteler call-off 集成测试 (ITC)。CoC 内部将进行预测试。<br>📅2025-06-30: Dry Try in progress<br>📅2025-06-23: ULP setup is missing<br>📅2025-06-16: BOM not in P-System, but found in K-System (no SDSA, no demand in K-System) -
| WP4 | I01 Call-off (Benteler full scenario) (ULP prep.)<br>I02 ZF Interface verification | 📅2025-06-30: Dry Try in progress<br>📅2025-06-23: ULP customizing in progress.-
| WP5 | Outbound shipping & Seeburger| 📅2025-06-30: in check with WP1<br>📅2025-06-16: HU creation by WP1 for test prep. "outbound shipping", currently setup of WP1 in progress (check if the creation can be done manually)-
| WP6 | No test case defined in Test Part 1. | 📅2025-06-30: Test plan need to be aligned with MBFS together.<br>📅2025-06-23: setup "Production Version" for the test parts. Test schedule in alignment with MBFS.-

### Reference

- 涉及实体: Benteler, ZF, Seeburger, MBFS
- 核心依赖: BOM, ULP, HU Creation

---

## ET1 Line Supply

### Summary

关于 ET1 的线边供应流程，经过讨论已达成一致。尽管 ET1 试装阶段的供应通常不采用标准的系列流程，但考虑到大部分零件并非 EX33 类型，项目决定 ET1 也将使用系列流程，即从仓库 Call-off 完整的载具。此话题已关闭。

### StoryLine

📅2025-06-25: Wen Juan (物流运营) 提出疑问，ET1 的线边供应通常只提供所需数量，而非完整的载具，不遵循系列流程。

📅2025-06-25: Tim Bawolski 回复，由于大部分零件不是 EX33 零件，希望 ET1 也采用系列流程，从仓库 Call-off 完整的载具。

📅2025-06-30: 此话题已关闭。

### Reference

- 核心人员:
  - Wen Juan
  - Tim Bawolski

---

## Solution Documentation (QM)

### Summary

与质量管理（QM）相关的解决方案文档已经完成，并于 2025 年 6 月 24 日向相关方进行了展示。项目团队已触发了该文档的签核流程，目前正在等待业务部门、本地 IT 和 CoC 团队的审批。

### StoryLine

📅2025-06-10: 计划在 2025 年 6 月 24 日（周二）进行解决方案的展示。

📅2025-06-24: 解决方案文档 `20250624 Solution Document eATS QM.pptx` 完成展示。

📅2025-06-30: 项目团队已触发签核流程，正在等待业务部门、本地 IT 和 CoC 团队的批准。

### Reference

- 主要文件: 20250624 Solution Document eATS QM.pptx
- 涉及部门: QM, 本地 IT, CoC
