﻿---
title: "AMSJ AMSR"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# AMSJ AMSR

COC 介绍: [20211112 AMSJ - logic PPT .msg](es://20211112%20AMSJ%20-%20logic%20PPT%20.msg) // 信息交旧, 以[Boardmix](https://boardmix.cn/app/share/CAE.CLClow0gASoQfjGPeE-Ied9bPfzW_8_WzTAGQAE/UQ2laM?elementNodeGuid=1:7)为准持续更新

1. 零件状态变动为 30 后会触发 idoc sent to DIALOG 更新 AMSJ
2. 有了AMSJ后,可以打散BOM并反冲(应LSO要求提前反冲)
3. IMS后status 40, 需求可见.(也可以手动修改到40)

    1. LSO发出订单后状态也会变成40 [20230428140130 Test in KSV Material status should change to 40 after lso ordering.msg](es://20230428140130%20Test%20in%20KSV%20Material%20status%20should%20change%20to%2040%20after%20lso%20ordering.msg)

![image](assets/image-20250228161139-zowfd65.png)

不能更改激活条件从 30 到 10 因为系统需要 status 变化来触发.

DIALOG 方面也可以手工维护 AMSJ [20211112 答复 H243 PT1 C2排序问题-紧急.msg](es://20211112%20答复%20H243%20PT1%20C2排序问题-紧急.msg)

‍

如何调整AMSJ/AMSR:

> BIW改为AS，需要: 移除AMSR标识
>
> AS/BS 共享件:
> 1 检查物料主数据工厂级别MRP controller 是BS 还是AS
> 2 查Dialog 数据，找出那些与工厂级别属性不一样的model Part ID ,工厂级别定义的是BS, 找出AS对应的Model ID
> 3 通知Docu 加上Deviating MRP controller
> AS改为BIW，MM02任意更改这个“Plant-sp.matl status”的值，保存，然后再MM02改回40，保存。系统晚上会自动更新需求，第二天就自动添加上AMSR。
> 如果着急需要联系SAP Helpdesk，让其通过/DA0/4010_F61推送AMSR给DIALOG。但TBE反正隔天才会run

‍

可以通过SE16N的Table [/DCI/RQI_MB_MAS](Projects/AmS@BDA/数据获取/Table%20SE16N.md#20220906115340-7hxcke0) 查看AMSR/AMSJ信息

通过/DA0/4010_CHECK_AMSJ 检查AMSJ是否因为某些原因没有成功发给DIALOG, 考虑使用/DA0/4010_F61重发

![image](assets/image-20250228160752-mivmk09.png)

‍

‍

[答复_ A0009933460需求问题_12_10_2022 - AS BS 需求位置问题.pdf](es://答复_%20A0009933460需求问题_12_10_2022%20-%20AS%20BS%20需求位置问题.pdf)

‍

‍

‍

‍

## Phase out indicator

> 📅20201015
>
> [李明奎](CRM/李明奎.md) : IPT 中 MM02 更改 KZAUS 标识， 可以实现零件不产生真车需求 或是不产生预测需求。AMS 中没有该功能
>
> [Fw_ 答复_ Re[2]_ 答复_ IPT中即将phase out的零件，可以加上标识_29_10_2020.pdf](es://Fw_%20答复_%20Re[2]_%20答复_%20IPT中即将phase%20out的零件，可以加上标识_29_10_2020.pdf)
>
> AMS 中可以通过锁 AmSJ 的 Reciever 来达到相似的效果

‍

