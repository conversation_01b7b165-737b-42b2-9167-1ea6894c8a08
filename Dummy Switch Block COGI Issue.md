﻿---
title: "Dummy Switch Block COGI Issue"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "问题解决"

category: "Issue"
project: "系统优化"
---
# Dummy Switch Block COGI Issue

## ⭐️ Key

Switch Block即将频繁甩件, 产生COGI, 冯智让提前进行处理准备

‍

|**PN**|**Car line**|
| -------------| -------|
|A2139051613|V213|
|A2139050913|V213|
|A2139051413|V213|
|A2139053613|V213|
|A2139051713|V213|
|A2139053513|V213|
|A2138272000|VW205|
|A2139050513|VW205|
|A2059054119|VW205|
|A2059056419|VW205|
|A2059055519|V205|
|A2539055803|V 253|
|A2539055203|V 253|
|A2539055903|V 253|
|A2539055603|N 293|
|A2939055000|N 293|
|A2939054500|N 293|

## 📆 Storyline

📅20210603 冯智在早会上提出注意COGI清理事项. 放入[cogi-weekly-g4yeh](COGI.周报.md)

📅20210610 在 `COGI 状况分析周报_CW23.pptx` 初次报告，需要补充零件-COGI-Vehicle 的关系

📅20210617 在E2 Project Meeting上展示单独的报告[Part Shortage Impact on COGI - v2 - 20210617](es://Part%20Shortage%20Impact%20on%20COGI%20-%20v2%20-%2020210617)，其中第一页介绍了“Why there is COG”

‍

