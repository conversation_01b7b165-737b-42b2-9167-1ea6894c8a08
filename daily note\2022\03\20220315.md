# 20220315

# 在AMS中修改零件BZA

在2021年的MFA数据迁移到AMS过程中, 部分零件由COC直接在AMS中修改了BZA, 咨询IT和财务后, 得到反馈: AMS支持直接在本地修改零件BZA, 但之后随着DIALOG数据传入, BZA会被覆盖为DIALOG版本, 因此不会对财务产生影响.

[20220315 AW PSV rehearsal - Result analysis 0709.msg](es://20220315%20AW%20PSV%20rehearsal%20-%20Result%20analysis%200709.msg)

📅20230404 本地没有权限修改的时候，吴立新通过重新处理Idoc的方式更改。

📅20230504 RD同事修改DIALOG

[20230504094328 答复 FL226013 设置PEM日期.msg](es://20230504094328%20答复%20FL226013%20设置PEM日期.msg)
