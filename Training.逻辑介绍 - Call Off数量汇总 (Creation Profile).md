﻿---
title: "Training.逻辑介绍 - Call Off数量汇总 (Creation Profile)"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 逻辑介绍 - Call Off数量汇总 (Creation Profile)

‍

# 💻 逻辑描述

零件订单在发送给供应商时, 往往会对长周期数量进行汇总, 例如 1 个月内发送订单精确到日, 1-3 个月内汇总为每周一个订单, 3 个月外汇总为每月一个订单.

该汇总逻辑通过 SA 主数据中的 `Creation Profile` 字段进行配置.

![image](assets/image-20220727152235-a356pb7.png)

`PLOC` 代表发出的订单在 1 个月内数字会精确到日, 1-2 个月内汇总为每周一个订单, 之后汇总为每月一个订单. 一般用于国产零件.

![image](assets/image-20220727151805-j8irpml.png)

`PGSS` 代表发出的订单在 6 个月内数字会精确到日, 6-9 个月内汇总为每周一个订单, 之后汇总为每月一个订单. 一般用于进口零件.

![image](assets/image-20220727152022-yqnbus1.png)

# 🧾 简单举例

从 ME39 或是其他的 Call Off 记录查看工具中可以看到, 订单数量按照 `PLOC` 的逻辑进行了汇总.

![image](assets/image-20220727152433-i78vzhm.png)

# 📈 进阶讲解

该字段在通过 DISCO 进行 SA 创建的时候由系统自动分配默认值, 在 AMS 上线初期通过零件 BZA 属性来进行分配, 于 2022 年 4 月经 IT 配置调整为根据供应商来配置字段的默认值.

# 📰 文档信息

> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>
> 初次发布时间：2021-10-08
>
> 最后更新时间：2022-07-27
>
> 版本: V1.2

‍

