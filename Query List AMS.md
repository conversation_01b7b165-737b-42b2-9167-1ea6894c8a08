﻿---
title: "Query List AMS"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# Query List AMS

# /DA0/0080_DI : Queries in MRP

## /DA0/0080_BED, monthly demand summary

只按照月份汇总，会把两年的数值加到一起。前面可以选起始日期为明年，但没有截止日期，没什么用。

![image.png](assets/image-20210927143941-8ncdnfy.png)

## /DA0/KPI_DISPO SA 基本信息

![image.png](assets/image-20210927144753-hayeeyq.png)

## 没有 Variant 无法使用

/DA0/PUS_SET

/DA0/0080_LPUE，Check overdelivery

/DA0/WEEING

## /DA0/WEEING Unloading Point 的 Area 和 SL 信息

![image.png](assets/image-20210927145231-3cgqulj.png)

## CHECK_AUS_UEB 零件库存，按 SL 分类

![image.png](assets/image-20210927145832-2k8gdup.png)

## DA0_0020_WBEW 移动记录汇总 mvt

![image.png](assets/image-20210927151604-81rdljt.png)

## DA0_0020_WBEWG 零件移动记录平铺 mvt

![image.png](assets/image-20210927152201-1lrpz7w.png)

‍

## MRP_DEPREQ Query 显示零件 DepReq 需求及位置

Global area

![image.png](assets/image-20210927150233-1u2dej7.png)

📅20210923 Martin 在 Q 开发完成,预计 9 月 26 日转入 PSV.

![image.png](assets/image-20210923140920-c6aw06d.png)

📅20211123 如何添加权限给 Global DepReq

> 吴立新：
>
> 将用户 ID 分配到 global user group /DA0/0080_DI
>
> ![image.png](assets/image-20211123144802-v5wjw92.png)

# WP3_MIG

WP3_0016, 查询车型、建筑、MRP_Area的关系。联动SE16N中的[/DA0/0090_HBERID](Table%20SE16N.md#20221014084726-4ifkycv)和[/DA0/0090_PRHALL](Table%20SE16N.md#20221014084810-2ym7by9)

