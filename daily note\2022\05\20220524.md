# 20220524

# BS Warehouse 145 - V254 common KLT UNLP change - 沈黛薇

## ⭐️ Key

> ![image.png](assets/image-20220525104610-lqb8uvq.png)[GitMind](https://gitmind.cn/app/flowchart/a1176e893d08f11b082a5563bd07e0bc)

2023年3月已调整[library-system-planning-cancel-104-order-and-transfer-to-bs2-z1rk3o9](Projects/AmS@BDA/一号库系统规划%20-%20取消104订购%20转入BS2.md)

## 📆 Storyline

📅20220524 在23日的会议上, ShenDaiwei介绍了目前145库房migration的问题. 因为是第一次接触这个话题, 没有很好的理解内容, 因此今日单独开会, 介绍逻辑和问题. 参会: 李妍妍, 程加林

结合[AmS@BDA_Organizational Structure_V0.99f.pptx](es://AmS@BDA_Organizational%20Structure_V0.99f.pptx), 和沈黛薇的描述.

### 145库房逻辑

![image.png](assets/image-20220524154431-l8bb83a.png)

因为IPT功能限制, 254的KLT零件送到145库房进行收货后直接送到220, 再由运营人员转库254相关需求到124BS.  
AMS上线前, 借机对此流程进行梳理.  
从文件上看, 145中的一个区域会和124一样归为104_BS2. UNLP 513X. 这样254车就会在一个车间里了.  
从沈黛薇描述上看, 104_BS2中设计上只有254 KLT Common. 也就是说这类Common零件的需求和零件将分别SOBSL到104和104_BS2中.

而从下面的途中可以看到, IT确实设计了装焊零件来自145的两个不同的区域.

![image.png](assets/image-20220524160042-frdyaui.png)

‍

### 问题

沈黛薇的问题是, 目前104和104_BS2下都是一个Storage Type NHA, 需要新建或合并.

我的疑惑是, 220BS的GLT零件和KLT零件分别放在了不同的MRP Area下面, 需求是如何传递的?

📅20220525 Dennis 答复WP2和WP6会配置系统, 让需求可以显示在两个地方, 而用户需要根据零件KLT/GLT而维护不同的UNLP. 根据沈黛薇答复, 原因是物理空间不足放不下GLT零件.

![image.png](assets/image-20220525104610-lqb8uvq.png)[GitMind](https://gitmind.cn/app/flowchart/a1176e893d08f11b082a5563bd07e0bc)

📅20220526 同MRP会议, 需要知道谁来提供KLT/GLT信息. 沈黛薇答复是赵晓曦.

经沟通, 另有一个问题, 是MRA1和MRA2的214 BS零件也都来自一号库, 目前考虑到运营上是两个不同的科室, 因此会在214项目再讨论此事,

![image.png](assets/image-20220526151310-4gsw84f.png)​

## 🔗 Reference

### 涉及人员

[李妍妍](CRM/李妍妍.Lydia.md) [程加林](CRM/程加林.md)

[shen-daiwei-z5sec4](CRM/沈黛薇.md) [zhao-xiaoxi-2hs4gh](CRM/赵晓曦.md)

[Dennis Kussmaul](CRM/Dennis%20Kussmaul.md)

[禄魁](CRM/禄魁.md) [杨一鸣](CRM/杨一鸣-LOG.md) [付近近](CRM/付近近.md)

‍
