﻿---
title: "AMS系统需求问题解答"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
## PEM点生效与系统需求显示时间差异说明
问题：
零件： A 223 860 11 03 (内部代码 8AC4)
背景： 该零件正在进行进口转国产JIS（Just-In-Sequence）切换。
关键日期：
国产货源生效日期：4月18日
CGM (变更管理?) 生效日期：11月3日
PEM (生产执行里程碑?) 点设置日期：11月3日
系统状态： PEM点已设置，但相关的TBE (零件需求计划?) 尚未运行。
疑问： 为什么设置了11月3日的PEM切换点后，系统却将12月份的需求分配给了国产货源，而不是从11月3日之后的需求就开始切换？PEM点的生效是否需要等待TBE运行？
解答：

PEM点的作用： PEM点的设置是触发需求切换到新货源（国产JIS区域）的关键。一旦PEM点设置完成（11月3日），系统逻辑上就认为从该时间点起，需求应由新货源满足。
TBEP与货源无关： TBEP（零件需求计划）本身不决定使用哪个货源，它只包含零件、车型、需求日期和数量信息。
PEM生效无需等待TBE运行： PEM点的设置使相应的TBEP（或说其代表的需求期间）在货源分配逻辑上立即生效，不需要等待TBE的运行。TBE运行主要是进行需求的计算和更新。
需求显示差异原因（核心解释）：
从11月份开始，该零件的TBEP是以“月度”为单位进行计划的，其内部需求日期可能被统一设置为11月1日。
用户在MD04（库存/需求清单）中看到的并非原始的、按天产生的需求，而是经过系统自动平均处理后的月度或周度需求。
虽然PEM点和月度TBEP是从11月开始指向国产JIS货源，但经过系统的需求平均逻辑计算后，这个变化在MD04的显示层面可能要到12月份才能明显体现出来。
