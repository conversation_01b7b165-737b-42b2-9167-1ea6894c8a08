﻿---
title: "All-in-one Material Info Replace DFJ19_MDCR"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# All-in-one Material Info Replace DFJ19_MDCR

# 猸愶笍 Key

鏇夸唬IPT涓殑`/DFJ/19_MDCR`, 琛ュ己AMS涓殑Query `WP3-MIG-0006-SA`

DRF 1000001472 

[馃搮20220505 鍚寸珛鏂拌皟鏁碊ynamic Safety Time, 骞舵斁鍏SV涓? SQ00-WP3_MRP-WP3_ALLINONE 20220505 MDCR in AMS upda...](#20220505135227-dbngvt7)

# 馃搯 Storyline

馃搮202111081710 email 鍒樹繆, 鍥犱负card閲岄潰鍐?

> BU needs to describe requirement together with local IT, CoC will support implementation during an AmS sprint.

馃搮202201201531 email to <PERSON>, 鍜ㄨ涓嬪懆涓€鐨凱M浼氳

馃搮20220124 閫氳繃PM Call

馃搮202202081404 鍒涘缓DRF 1000001472

馃搮20220211 <EMAIL> 閫€鍥? 闇€瑕佷慨鏀?
> German Management Summery is missing
>
> You have chosen 鈥淜VP鈥?category then I need a potential in Euro in case the demand will realized
>
> You declared AmS Budget This is not possible in case of BDA project. You have to use BDA budget.

馃搮20220228 MRP 鎻愪緵HC涓?.01, 鎶樼畻RMB涓?00

[20220228 RE Requirement 1000001472 - 'Query - All in one Material master  SA' zur Pr眉fung.msg](es://20220228%20RE%20Requirement%201000001472%20-%20'Query%20-%20All%20in%20one%20Material%20master%20%20SA'%20zur%20Pr眉fung.msg)

馃搮20220301 email 鍒樹繆, 鎻愯re-submit

馃搮20220328 Daily涓奀OC鎻愬嚭, 鎯充簡瑙ｄ笅鎶€鏈粏鑺? email缁欏惔绔嬫柊, 绛夊緟鍏剁瓟澶?

馃搮20220408 [Martin Otto](CRM/Martin%20Otto.md)鍒涘缓浜哷鈥淢ARTIN_TEST鈥?in the WP3_MRP group on KSV`, 寮€濮嬫祴璇?

馃搮20220421 MRP鎻愬嚭搴旇浣跨敤Dynamic Safety Time鑰屼笉鏄疭afety Time, 鍜ㄨMarin鍚? 瀵规柟璁や负鍚寸珛鏂板彲浠over, 绛夊緟鍚寸珛鏂扮瓟澶?

馃搮20220505 鍚寸珛鏂拌皟鏁碊ynamic Safety Time, 骞舵斁鍏SV涓? SQ00-WP3_MRP-WP3_ALLINONE [20220505 MDCR in AMS updated.msg](es://20220505%20MDCR%20in%20AMS%20updated.msg)


