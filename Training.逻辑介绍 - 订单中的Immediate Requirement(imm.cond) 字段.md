﻿---
title: "Training.逻辑介绍 - 订单中的Immediate Requirement(imm.cond) 字段"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 逻辑介绍 - 订单中的Immediate Requirement(imm.cond) 字段

‍

# 💻 逻辑描述

订单(Schedule Line)的数量在 Call Off 发送当天如果存在当日内增加的情况, 那么增加的数量会被计入 Immediate Requirement. 系统认为这部分增量时间紧迫, 需要重点关注.

# 🧾 简单举例

下例中, 在 21 日的 Call Off 中 22 日需要 100 个零件, 但在 22 日当天的 Call Off 中这一数量增加到了 150 个, 系统会把多出的 50 个标注计入 Immediate Requirement.

Call Off 日期 : 7 月 21 日

|订单日期|数量|
| ----------| ----|
|7 月 22 日|100|
|7 月 23 日|100|

Call Off 日期 : 7 月 22 日

|订单日期|数量|
| ----------| ----|
|7 月 22 日|150|
|7 月 23 日|100|

如下图, 零件标注有 900 个 imm.cond. 从 Call Off 记录对比上看, 确实当日的 Call Off 数量有 900 个新增.

![Untitled](assets/Untitled%203-20220726090758-0sykpx0.png)

# 📈 进阶讲解

请留意, 这 900 个被计入 imm.cond.后, 7 月 22 日的数量显示为 0.

![Untitled](assets/Untitled%202-20220726090758-9k67fun.png)

# 📰 文档信息

> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>
> 初次发布时间：2022-03-15
>
> 最后更新时间：2022-07-22
>
> 版本: V1.1

