﻿---
title: "AMS.更改需求显示逻辑回到IPT模式"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# 更改需求显示逻辑回到 IPT 模式

## Summary (项目摘要)

**核心问题**: AMS 系统需求显示逻辑与 IPT 模式存在差异，导致与生产计划差异较大。根本原因在于[AMS 中 MD04 数值与车号脱钩](更改需求显示逻辑回到IPT模式/AmS.BDA.Gaps.Missing%20DepReq%20in%20MD04.md)。

**当前状态**: 最终保留使用 AMS 的新逻辑，未切换回 IPT 逻辑。原因是 IT 无法 100%保证切换后不会出问题，而用户 MRP 要求完全保证。

**关键结论**:

- IPT 逻辑：BOM 在上 BS 后锁定，需求日期不随排产变化
- AMS 逻辑：BOM 永不锁定，系统根据节拍、序列、工位自动推算需求日期
- 更改逻辑无法解决 AMS 中的核心问题（需求不减少、特殊需求被反冲等）

## StoryLine (演进史)

📅20211011 [ams-bda-fnrcj](Projects/<EMAIL>)上线后, [付近近](CRM/付近近.md)反馈新的系统需求逻辑下与生产计划差异比较大

创建 Solman Ticket

📅20211013 WP2 反馈可以改回 IPT 的显示逻辑,但是无法保证不会出问题.

[李明奎](CRM/李明奎.md)反馈逻辑区别: 总结来说, AMS 中 BOM 在车辆销售前不锁定. 系统根据节拍,序,工位来推算需求日期.

[20220127 答复 Meeting Minutes_H247 系统逻辑.msg](es://20220127%20答复%20Meeting%20Minutes_H247%20系统逻辑.msg)

> 先说 IPT 逻辑
>
> 1. 上 BS 之前的订单，排在几号，零件需求就展示在几号
> 2. 上 BS 之后的订单，BOM 锁定，零部件需求锁定，不随着订单排产日期的变化而变化
> 3. 欠产或超产情况下，因为涉及已经上了 BS，零件需求日期不会发生任何变化
>
> 再说 AmS 当前逻辑
>
> 1. 订单 BOM 在车辆销售之前，永远不锁定
> 2. 系统根据下面的参数，自动推算所有零件的使用日期，推算到哪天，需求就显示在哪天。  
>    -》 生产节拍（根据产线的设计速度）  
>    -》 订单上线序（来自于 ASF 的珍珠链）  
>    -》 零件使用工位信息（来自于 WP1 的 control cycle）  
>    3. 对于欠产或者超产的情况，不影响上述逻辑。每天晚上系统会根据当天的实际生产情况，以及待上线订单序列，重新计算所有零件的需求日期。
>
> One more option in AmSupply  
>  AMS 系统可以 switch off 当前逻辑，实现  
>  -》 ASF 的订单的总装上线（IB）计划在哪天，所有 BOM 零件的需求就展示在同一天。谨记： IB Plan date = demand date  
>  -》 欠产情况下， ASF 里每天晚上会将当天未上线的订单，reschedule 到第二天，零件需求也会相应地移动到第二天。谨记： IB Plan date = demand date  
>  -》 超产情况下，相当于是订单提前生产，当天会提前消耗次日的需求。第二天随着 ASF 的订单 reschedule，后面的订单会陆续提前，零件需求也会陆续提前。谨记： IB Plan date = demand date  
>  -》 对于较晚工段/工位（如 C3/EOL 等）使用的零件，车辆生产从进入 IB，可能当天到不了反冲工段/工位，这部分需求不会移动到第二天，会一直挂在 IB plan 的当天。谨记： IB Plan date = demand date  
>  -》这种逻辑测试的时候没问题，不过在戴姆勒其他工厂的 AmSupply 中没有使用的先例。

‍

📅2021105 PM Call 会议上提出这个事情, [Ruestem Tuerksoy](CRM/Ruestem%20Tuerksoy.md)建议用顺义进行测试后更新到 BDA.

之后联系[董文哲](CRM/董文哲.md)和[刘波](CRM/刘波.md)会议介绍该事,KSV 系统已调整,顺义使用 KSV 系统验证.

📅20220127 目前 KSP pre-assemly 预分装 线的反冲点设置在线边, 而实际使用时间要远远早与这个反冲时间, MFA 同 WP2 协商增加 3 个反冲点. [20220308 答复 预分装工位系统配置问题.msg](es://20220308%20答复%20预分装工位系统配置问题.msg)

📅20220125 刘波和 BDA MRP 对这个调整的必要性在持续沟通中.  
![image.png](assets/image-20220127155905-xdevyoq.png)

[20220127 答复 AmSupply Demand calculation logic clarification.msg](es://20220127%20答复%20AmSupply%20Demand%20calculation%20logic%20clarification.msg)

建议同步联系李明奎, 制作升级用的 PPT 文件, 对比逻辑变化, 风险.

📅20220217 李明奎开会介绍 AMS 的需求逻辑

找零件对比 0753 时间和 MD04 的时间, 发现下午的都会显示到第二天. 联系明奎后, 他来介绍系统需求逻辑.

![202202171003.png](assets/202202171003-20220217100554-40xx1xg.png)  
​![Snipaste_2022-02-17_10-01-40.png](assets/Snipaste_2022-02-17_10-01-40-20220217100554-yzx2f6o.png)

![Snipaste_2022-02-17_09-59-48.png](assets/Snipaste_2022-02-17_09-59-48-20220217100612-ts6hsxs.png)

((20220317161231-qjka9g2 '逻辑介绍 - AMS 零件需求显示逻辑 - ITP DPP - working')) 📅20220317

AMS, factory layout, 激活 ITP 之后, demand 会考虑工位的预计使用时间.

系统 0230 时间检查 table `/DA0/0050A_FLBDF`(各线的最后一台反冲车号)和`0050A_FHLPK`(珍珠链)

在凌晨 0130 job run 的时候, 检查没过工位的车数, 然后推后这部分需求.同时推后第二天的车辆数 DP.

DPP = Daily Production Program

目前 KSP 按照反冲工位来说在合装位置, 处于底盘线后面, 所以虽然预分装的时间很早, 但是系统需求按照实际的安装工位来推算时间. 目前管理科在同 WP2 讨论考虑在 shopping cart 位置增加反冲点, 让需求提前反冲, 系统逻辑也能提前进行计算. [张国斌](CRM/张国斌.md), [yang-xingjuan-zsndvj](CRM/杨兴娟.md), [鲁志远](CRM/鲁志远.md), [MFA 要求增加三个反冲点](#20220308141350-iwt9dji)

[Demand Calculation in AmS.pptx](es://Demand%20Calculation%20in%20AmS.pptx) - 李明奎准备的需求逻辑说明文件

[20220222 17.Feb_Meeting Minutes_MRP Regualr Meeting.msg](es://20220222%2017.Feb_Meeting%20Minutes_MRP%20Regualr%20Meeting.msg) - 会议纪要

📅20220304 更改逻辑能否解决目前 AMS 中的问题?

> 1. [AMS MD04 Demand Logic - Demand in tomorrow not reducing](更改需求显示逻辑回到IPT模式/AMS%20MD04%20Demand%20Logic%20-%20Demand%20in%20tomorrow%20not%20reducing.md).
> 2. Series Backflush is reducing special demands. [Special Demand 被自动反冲](Special%20Demand%20被自动反冲.md)
> 3. Demand date freeze. (ex. today planned 100, produced 80. These 20 will be fixed on today until backflushed?)

> 1. 不能.
> 2. 不能.
> 3. 每天晚上会更新. 所以不会有 fix 在过去的.

📅20220407 一般情况下生产计划不会因为欠产情况而调整 ASF DPP, 因为涉及到的系统调整工作很多.

IPT 因为 BS 锁定需求, 所以能够看出欠产, 计算的时候考虑的 demand 大. 但是 AMS 紧随 ASF, 只能调整 ASF.

📅20231018 补充, 根据记忆, 最后没有切换回 IPT 逻辑, 因为 IT 说没有发现技术风险但不能保证 100%没问题, 用户 MRP 要求 IT 保证完全不会出问题否则不切回 IPT 逻辑. 最后就一直保留使用 AMS 的新逻辑了.

## Reference (参考资料)

**主要文件**:

- [Demand Calculation in AmS.pptx](es://Demand%20Calculation%20in%20AmS.pptx) - 李明奎准备的需求逻辑说明文件
- [AmS.BDA.Gaps.Missing DepReq in MD04](更改需求显示逻辑回到IPT模式/AmS.BDA.Gaps.Missing%20DepReq%20in%20MD04.md)

**核心人员**:

- [李明奎](CRM/李明奎.md) (系统逻辑专家)
- [付近近](CRM/付近近.md) (用户反馈)
- [董文哲](CRM/董文哲.md), [刘波](CRM/刘波.md) (KSV 系统)
- [Ruestem Tuerksoy](CRM/Ruestem%20Tuerksoy.md) (PM)

**相关文档**:

- [20220127 答复 Meeting Minutes_H247 系统逻辑.msg](es://20220127%20答复%20Meeting%20Minutes_H247%20系统逻辑.msg)
- [20220222 17.Feb_Meeting Minutes_MRP Regualr Meeting.msg](es://20220222%2017.Feb_Meeting%20Minutes_MRP%20Regualr%20Meeting.msg)
- [AMS MD04 Demand Logic - Demand in tomorrow not reducing](更改需求显示逻辑回到IPT模式/AMS%20MD04%20Demand%20Logic%20-%20Demand%20in%20tomorrow%20not%20reducing.md)
- [Special Demand 被自动反冲](Special%20Demand%20被自动反冲.md)

