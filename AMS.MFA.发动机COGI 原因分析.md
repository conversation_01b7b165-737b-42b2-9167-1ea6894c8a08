﻿---
title: "AMS.MFA.发动机COGI 原因分析"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# AMS.MFA.发动机COGI 原因分析

## Summary

AMS 上线 MFA 后, 吴迪组织分析 COGI 的产后生原因.

以下是COGI问题中涉及的部分发动机零件号：
> A2473703200
>
> A2473700900
>
> A7003702700
>
> A2820103103
>
> A2820103203
>
> A2600109000
>
> A2820103303
>
> A2820103403
>
> A2820103603


## 📆 Storyline

📅20211111 初次会议

- 反馈 ENGINE/BP 使用 JIT 模式供货原因

📅20211118 会议分析发动机 COGI 产生原因

![image.png](assets/image-20211118134003-jmajqpf.png)

- 吴迪现场盘点, 发动机系统中在 TRIM0 线上, 存在提前反冲远远大于 BUFFER 数量的问题.
- 发动机 BUFFER 区存放 72 个, 线边 7 个.
- 琚鲁宁反馈检查 TRIM0 原因, 零件根据 PSA 定义反冲位置, 因为发动机没有 control cycle 所以线头反冲.

📅20211119 已给发动机设置好工位

![image.png](assets/image-20211126152043-5oa284t.png)
- 问题解决：增加发动机工位

## 🔗 Reference

- **相关人员**:
  - 吴迪 (COGI 问题分析组织者)
  - 琚鲁宁 (TRIM0 原因反馈人)
- **相关文档**:
  - AMS.MFA.发动机COGI 原因分析.md (当前文档)

