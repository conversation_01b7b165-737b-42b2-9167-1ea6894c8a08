﻿---
title: "JIS Stock Clean to 0"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "JIS"

category: "Project"
project: "JIS系统"
---
# JIS Stock Clean to 0

# ⭐️ Key

## 背景

> 接受审计时发现, JIS 系统库存会不断产生, 建议指定流程进行清理.​` 北京奔驰物流运营部 JIS 件系统库存清理流程 draft V2 20210805 - 吴晨.pptx`​

每周进行自查, 初步原因分析, 5 个工作日内反馈 IT, 由 IT [赵莫凡](CRM/赵莫凡.md)支持.

## 原因

1. 收货按JIS-P, 反冲按BOM. 但实际上BOM和JIS-P都在BS start时锁定.

2. 系统主数据维护问题

    1. 切换时间差异, 例如JIS/JIT系统切换

# 📆 Storyline

📅20210624 杨文/吴晨组织会议介绍目前的JIS Stock清理流程设计

**JIS GR and BF comparing report**

![image.jpg](assets/image-20210628110949-xbf320c.jpg)

> 程序是 2010 年创建的，最后一次修改时间是 2011 年。德国 JIS 的 COC 和我都没有使用过。我们在职期间也没有收到过业务对这个报表的问题。  
> 这 10 年间 JIS，OM 的逻辑都改过，所以这个报表不太适用

📅20210721 同 AMS WP1 澄清, 这类话题不属于 AMS WP3

![image.png](assets/image-20210805115548-uilkmhe.png)​

📅20210811 刘畅.财务提出方案, `20210813 转发 Log JIS clearance background clarification.msg`​​ , 需要同各业务部门 align

![image.png](assets/image-20210813092513-apkpfba.png)

# 🔗 Reference

[JIS GR & GI correction功能加强](JIS%20GR%20&%20GI%20correction功能加强.md)

[AmS.BDA.JIS.Reorder](AmS.BDA.JIS.Reorder.md)

