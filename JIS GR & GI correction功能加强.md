﻿---
title: "JIS GR & GI correction功能加强"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "JIS"

category: "Project"
project: "JIS系统"
---
# JIS GR & GI correction 功能加强

## Summary (项目摘要)

### 事件起因 - 因缺少 COGI 而在 SHU 造成的 JIS Correction 问题

📅20201021

[<PERSON>](CRM/Martin%20Otto.md)介绍： IPT 所 有 BOM 内根据 linefeed 信息反冲 AMS 只有设置正确的零件才被反冲 AMS 中 JIT 零件库存不足时也会生成 COGI，但 JIS 方面只有正确 GR 的 JIS 零件才会被反冲，因此不会出现 COGI

> 1. BOM 在 EOL 之后更新导致零件未反冲
> 2. 作为 KON 零件在 flag X 下被反冲. (JIS 应当为 flag J)
> 3. 其他, 例如错误的主数据维护

![image](assets/image-20230717160315-insy03p.png)

![image.png](assets/image-20211111101624-151861i.png)

JIS Settlement Report - 顺义例子

### 相关 T-Code 列表

| T-Code                  | Des                                                                                                                                                                                                                                                                                                               | CoC                                                                                                                                                                                                                                                                                                                                   |
| ----------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| /DA0/0360A_JISGAPMON    | JIS GR Report 主命令                                                                                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                       |
| /DA0/0144_Q_PABGR       | Correction 记录                                                                                                                                                                                                                                                                                                   |                                                                                                                                                                                                                                                                                                                                       |
| /DFJ/13_JIS_POST_MAN    | Before vehicles are archived, users can add materials in the vehicle by this t-code. And the materials are recorded in settlement report. This is important for Finance. posting GR and GI                                                                                                                        |                                                                                                                                                                                                                                                                                                                                       |
| /DFJ/13_JIS_MAN_GR      | After vehicle archived, It posts GR to the schedule agreement, not relevant to the vehicle, not in the settlement report. So it’s bad for analysis in the further, e.g. we can’t check if the missing materials are finished manual GR for suppliers. only posting GR. After manual, user need to deal with COGI. |                                                                                                                                                                                                                                                                                                                                       |
| /DFJ/25_SETTLE_ARCHIVE  | IPT: allows archiving of the JIS Settlement Report, already processed JIS Settlement Reports can be generated again and a copy of each JIS Settlement Report is copied to a fileshare.                                                                                                                            |                                                                                                                                                                                                                                                                                                                                       |
| /DFJ/13_JIS_CREATE_DATA | creates JIS Calls based on a date/checkpoint combination. Therefore it is possible to select only a small time range for creation of the JIS Calls.                                                                                                                                                               | In AmS the creation of JIS Calls is controlled based in the vehicle status. Usually it set to create a JIS Call from status 3000 to 7500. Need to implement new primary status.                                                                                                                                                       |
| /DFJ/25_REDES_SETT_N    | Export JIS call (including archived vehicles) by material document.                                                                                                                                                                                                                                               | → Users can search settlement report in /DA0/0144_DIS_SETTLE by various criteria<br /><br />→ Archiving concept to be discussed<br /><br />→ Not sure what other functions this report allows                                                                                                                                         |
| /DFJ/ZGL_JIS_LIFNR      | Export JIS call and GR (including archived vehicles) by production number                                                                                                                                                                                                                                         | → JIS Calls (incl. archived vehicles) can be searched in /DA0/0147_JITOM. User can search there by various criteria an export list view as an Excel file.<br /><br />→ Not sure, what data this query exports, more details would be required.                                                                                        |
| /DFJ/13_OPEN_JIS_GR     | Report failure of JIS GR after backflush. EventHandel (EH), SJIS, receive JIS backflush point, then IPT gr and change JIS call status to 3200.<br />用这个命令检查已经 backflush，但是仍在状态 ≤3100                                                                                                              | Should never happen, but /DA0/0147_JITOM can be used to identify all vehicles that have passed the EoL checkpoint but have not been processed for GR posting                                                                                                                                                                          |
| /DFJ/13_JIS_MONITOR     | JIS change report: change log of JIS call，e.g. what materials are added, deleted                                                                                                                                                                                                                                 | → /DA0/0146_TRANSMON can be used to identify deltas between the generated JIS Preview messages (material x was added, material y was deleted, date changed, code changed etc).<br /><br />→ No change log on JIS Call level, but no other plant needs this.                                                                           |
| SLG1                    | Master data issues cause that JIS plan can’t be created.                                                                                                                                                                                                                                                          | SLG1 also includes some JIS errors, but I do not really know what the use case here would be. No other plant checks anything in the SLG1, so I do not see the use case for this in the AmS environment. More details would be required for what is checked there.<br /><br />in AMS not check source list, when JIS call so no error. |

### BDA JIS Correction 流程

1. 一般由供应商发起, 提交 MRP, 转交核算 - 202102120144
2. RD 确认零件标准单车用量
3. 物流核算确认相关 COGI
4. 提交财务申请 replenishment
5. 之后系统操作 replenishment 和清理 cogi
   1. COGI for recent vehicle e.g. due to Master Data issue: → use /DFJ/13_JIS_MAN_GR to post GR
   2. GR/GI missing for recent vehicle, e.g. due to BoM error → use /DFJ/13_JIS_POST_MAN to post GR + GI
   3. GR missing for already archived vehicle, e.g. due to BoM error → use /DFJ/13_JIS_MAN_GR to post GR – GI taken care of separately?

![image.jpg](assets/20201207144826-edrtjre-image.jpg)

### Training JIS Correction - 刘锦华

JIS 收货在 EOL, JIS 反冲在收货成功后才会做消耗一天一次

系统功能分为三部分:

![image.png](assets/image-20211111101103-cjivjv6.png)

AMS 反冲 flag, `J` 为 JIS 反冲, `X` 为一般零件反冲.

JIS 零件判断:

1. Delivery method = JIS
2. Backflush flag = J

### Disco for correction process

![image.png](assets/image-20211111101810-t29sk89.png)

3 种情况, 8 种状态.

![image.png](assets/image-20211111102926-wguc29z.png)

## StoryLine (演进史)

📅20240327: 根据审计意见, 组织管理科会议, 介绍 Report 原理. [Intro JIS GR Report_20240325.pptx](es://Intro%20JIS%20GR%20Report_20240325.pptx)

管理科反馈意见:

> 今天会议收到以下反馈意见:
>
> 1. 需要电子版操作指导
> 2. 在进行简单的分析和分类后, 需要其他部门的接口人员接收并进行后续的深入分析
> 3. 补反冲需要需求部门提交 OA 后进行操作, 因此在完成分析后如何继续下去还需要吗明确
>
> 欢迎大家补充.
>
> 接下来:
>
> 1. 和各位进行 1V1 的会议详细举例介绍如何进行系统分析
> 2. 明确其他部门接口人员以及补反冲的操作方式
> 3. 准备电子版操作指导 [Intro JIS GR Report_20240325.pptx](es://Intro JIS GR Report_20240325.pptx)

📅20240219: [📅20240129 E2 收到顺义 E3 Robin Sievers 的邮件, 其对审计报告有意见. 因此 E2 转来邮件, 要求对审计报告的内容进行逐条分析.回顾下 AMS 中的 JIS 反冲逻辑 JIS B...](用户账号权限审计/AMS账号权责一致性审计_2023.md#20240205111227-zwsc5qv)

📅20211111: 刘锦华安排培训

📅20210922: 提醒锦华, 预计节前完成培训. WP2 想让董硕来讲, 沟通中.

📅20210913: 戴敏提出要在 E3 Shopfloor 上介绍进展. 联系李明奎和刘锦华, 抄送 Ruestem.

📅20210819: Martin 介绍/培训方案 [DOCU_User_BF_Correction_JIS_PaB_20210819.pptx](es://DOCU_User_BF_Correction_JIS_PaB_20210819.pptx)

📅20210219: 会议介绍之后, [Axel Hallwachs](CRM/Axel%20Hallwachs.md) 发来介绍 PPT

[20210220090000-z4s38ch-AmS@BDA JIS Backflush - Analysis _ Cockpit _ Correction (WP 2 and WP 3) Concept.eml](<es://20210220090000-z4s38ch-AmS@BDA%20JIS%20Backflush%20-%20Analysis%20_%20Cockpit%20_%20Correction%20(WP%202%20and%20WP%203)%20Concept.eml>)

JIS 零件 GR 在车辆 EOL 之后, JIS 零件的反冲在 GR 之后(也可以选择固定频次反冲)

Gap 产生原因

1. BOM 在 EOL 之后更新导致零件未反冲
2. 作为 KON 零件在 flag X 下被反冲. (JIS 应当为 flag J)
3. 其他, 例如错误的主数据维护

新的 Correction 功能涵盖

- Report 来找 gap
- Cockpit 来管理 correction 流程
- Report 来 release correction

📅20201021: [Martin](CRM/Martin%20Otto.md)介绍： IPT 所 有 BOM 内根据 linefeed 信息反冲 AMS 只有设置正确的零件才被反冲 AMS 中 JIT 零件库存不足时也会生成 COGI，但 JIS 方面只有正确 GR 的 JIS 零件才会被反冲，因此不会出现 COGI

> 1. BOM 在 EOL 之后更新导致零件未反冲
> 2. 作为 KON 零件在 flag X 下被反冲. (JIS 应当为 flag J)
> 3. 其他, 例如错误的主数据维护

## Reference (参考资料)

### 主要文件

- [20200903\_ JIS PaB COGI AmS_V4.pptx](es://20200903_%20JIS%20PaB%20COGI%20AmS_V4.pptx)
- [JIS Process High Level Overview_Finance_Alignment_20201207144619.pptx](es://JIS%20Process%20High%20Level%20Overview_Finance_Alignment_20201207144619.pptx)
- [Re2 答复 答复 答复 答复 答复 AmS@BDA 差异 T-Code 讨论 (180 KB).msg](<es://Re2%20答复%20答复%20答复%20答复%20答复%20AmS@BDA%20差异T-Code讨论%20(180%20KB).msg>)
- [DOCU_User_BF_Correction_JIS_PaB_20210819.pptx](es://DOCU_User_BF_Correction_JIS_PaB_20210819.pptx)
- [20210220090000-z4s38ch-AmS@BDA JIS Backflush - Analysis _ Cockpit _ Correction (WP 2 and WP 3) Concept.eml](<es://20210220090000-z4s38ch-AmS@BDA%20JIS%20Backflush%20-%20Analysis%20_%20Cockpit%20_%20Correction%20(WP%202%20and%20WP%203)%20Concept.eml>)
- [Intro JIS GR Report_20240325.pptx](es://Intro%20JIS%20GR%20Report_20240325.pptx)

### 核心人员

- [Martin](CRM/Martin%20Otto.md)
- [Axel Hallwachs](CRM/Axel%20Hallwachs.md)
- 刘锦华
- 戴敏
- 李明奎
- Ruestem
- 董硕
- Robin Sievers

### 相关文档

- [用户账号权限审计/AMS 账号权责一致性审计\_2023.md#20240205111227-zwsc5qv](用户账号权限审计/AMS账号权责一致性审计_2023.md#20240205111227-zwsc5qv)

