---
title: "Database.MemoryBank"
summary: "项目术语和缩写数据库"
date: "2021-01-01"
updated: "2025-01-04"
status: "in-progress"
tags:
  - "数据库"
  - "术语"
  - "缩写"
  - "知识库"
category: "Database"
project: "知识管理系统"
---

# MemoryBank

## 1. 系统与工具 (Systems & Tools)

- AMS (AmSupply): AmSupply，一个系统名称。
- ASF: 一个生产相关系统。
- 安防系统: 用于安全管理的系统，在此项目中特指负责门禁、访客等功能的主系统。
- ATM (Active Transport Management): 主动运输管理，目前辛克(SB Schenker)为BBAC开发的系统，具有路途跟踪、卸货道口预约排队、卸货时间统计等功能。平时提及ATM时,也经常指代辛克(SB Schenker)负责运营ATM提供第四方运输服务的团队。
- cbFC (Central Business Finance & Controlling): 中央业务财务与控制系统。
- Dialog: 系统名称，用于 BOM 变更。
- DRF (Demand Request Form): 需求申请表。
- E3 Shopfloor: E3 车间管理系统。
- IPT (Integrated Production & Transport System): 集成生产与运输系统。
- JITOM (Just In Time Order Monitoring): 即时订单监控。
- MIGO: SAP 系统中用于货物移动的事务代码。
- MobSy / Mobsy: 移动扫描系统，用于收货操作。
- PLUS: 生产线系统接口。
- ProQ: 一个合同或质量管理系统。
- SEFU: 系统名称，与物流操作相关。
- SLG1 (SAP Application Log Display): SAP 应用日志显示事务代码。
- SQ00: SAP 系统中的查询事务代码。
- SYSU: 一个系统名称，与 JIS-P 流程相关。
- Tableau: 用于“运输车辆信息电子台账系统”的数据可视化工具。
- TRANSMON (Transmission Monitoring): 传输监控。
- WIW: 用于访问 MBAG 部分系统的账号类型。
- ZASM: 一个用于全球范围内锁定车辆或电池的系统。
- PBL (Pick By Light): 拣选指示灯系统，用于指导仓库拣选操作。
- Push Button: 叫件器按钮，在此上下文中指代一种用于触发操作的物理按钮设备。
- 方方格子: Excel 插件，用于数据格式转换和处理。
- SharePoint: 微软的协作平台，用于文档管理、工作流和业务应用程序。
- 星云: 公司内部的文档管理或协作平台。
- SDB (Supplier Database): MB 系统，用于创建供应商代码。
- EDI (Electronic Data Interchange): 电子数据交换，用于供应商与企业间的数据传输。
- OA (Office Automation): 办公自动化系统，用于审批流程。
- Seeburger: 用于 B2B/EDI 集成的中间件平台或服务商。
- SP01: SAP 事务代码，用于后台作业假脱机请求的输出控制器。
- K-System (K-sys): 在生产系统（P-System）之前，测试系统,例如 KSV,KSW。
- P-System (P-sys): 主要的生产系统，例如 PSV,PSW
- EDW (Enterprise Data Warehouse): 企业数据仓库，用于数据整合、分析和报告的系统。
- Qlik Sense: 一款商业智能（BI）和数据可视化工具，是 QlikView 的后继产品。
- QlikView: 一款商业智能（BI）数据发现和可视化工具，是 Qlik Sense 的前代产品。
- MO360: 戴姆勒的数字化生产生态系统平台。
- Azure: 微软的云计算服务平台。
- DISCO: 项目名称, 指代 "Initial Supply Chain Setup" 项目。
- START: 用于管理和发布电子物料清单（Digital BOM）的系统。
- Digital BOM: 电子物料清单，通过 START 系统进行管理和发布，以替代传统的Excel文件传递方式。
- Globus: 一个用于向AMS系统传送计划协议（SA）的系统。
- Commodity Meeting: 采购部门用于获取零件信息、协调供应商策略的会议。
- BVA (Baumuster-Verfügbarkeits-Analyse): 车型可用性分析。在BOM流程中，用于定义零件的关键属性，如来源地（进口/国产）、订购类型等。
- CPL1: BVA的一种定义，通常指进口零件。
- ZB: BVA的一种定义，指国产且通过MRP订购的JIS零件。
- RD/Local: BVA的一种定义，指由研发部门（RD）通过采购申请（PR）订购的国产零件。
- BZA: Buy back（回购）零件的标识。
- iteration: 项目迭代周期，特指当前正在进行的开发周期。
- Ondeso: 一款补丁管理系统，用于在此项目中为联网设备部署安全补丁。
- RDP (Remote Desktop Protocol): 远程桌面协议，一种允许用户远程连接和控制另一台计算机的协议。
- SCSP (Symantec Critical System Protection): 一款终端安全软件，用于保护关键系统免受攻击和未知威胁。
- Windows CE: 微软推出的一款用于嵌入式设备和移动设备的操作系统。

## 2. 流程与概念 (Processes & Concepts)

### 2.1 物流与供应链 (Logistics & Supply Chain)

- 2-step GR: 两步收货。
- ANL: 订单处理相关的系统或流程。
- Call Off: 采购订单或计划协议中，供应商根据需求分批次发货的指令。
- Creation Profile: 在 SAP 或类似系统中，定义物料需求计划 (MRP) 或计划协议 (SA) 中需求汇总和冻结期的配置参数。
- decant: 分拣/ переливание，在物流中指将大包装（如一个托盘）中的货物拆分到更小的单元或直接补充到线边。
- eATS (electric Axle Transmission System): 电动轴传动系统，也指代“电机”项目。
- frozen zone: 在物料需求计划 (MRP) 或计划协议 (SA) 中，指在特定时间段内需求或计划不能被更改的区域。
- GI (Goods Issue): 发货。
- GIPR (Goods Issue Production Receipt): 生产收货发货。
- GR (Goods Receipt): 收货。
- GRST (Goods Receipt Stock Transfer): 收货库存转移。
- HU (Handling Unit): 操作单元，在 SAP 中用于追踪和管理物料移动的物理单元（如托盘、箱子）。
- JIS (Just In Sequence): 按序供应。
- JIS-P (Just-in-Sequence for Production): 生产按序供应。一种与生产计划紧密集成的 JIS 模式。
- KON: 寄售物料。
- Linefeed 信息: 指示物料在生产线上的供应方式或顺序的信息。
- LSP (Logistics Service Provider): 物流服务提供商，第三方物流公司。
- mini package: 迷你包装，指一种小批量包装或发货方式。
- MMSA: 用于零件订购, 发出 call off 的 Schedule Agreement。
- MRP (Material Requirements Planning): 物料需求计划。
- Overdelivery: 超订单收货，指实际收货数量超过订单数量的情况。
- PSA (Production Supply Agreement): 生产供应协议。
- Replenishment: 补货，指为补充库存而进行的物料调拨或采购。
- Resequencing: 排序作业，供应商根据生产需求对零件进行重新排序的作业。
- RFQ (Request for Quotation): 询价流程。
- SA (Scheduling Agreement): 计划协议。
- SDSA: 用于零件生产, 接收 call off 的 Schedule Agreement。
- SJIS (Specific JIS): 特定 JIS。
- Trailer Yard: 挂车堆场，用于停放和管理运输挂车的指定区域。
- ULP / UNLP (Unloading Point): 卸货点。
- WTO (Warehouse to Order): 仓储到订单。
- 迷你供应商园区: 在公司工厂内部指定区域设立的供应商作业区域，用于将外部租赁仓库的供应商作业转移到内部。
- 运输车辆信息电子台账系统: 基于 Tableau 的车辆进出厂管理系统。
- 载具: 用于运输和存储零件的标准化容器或托盘。
- vendor change: 供应商切换功能，用于在系统中切换供应商代码。
- relevant for: SDB 系统中的状态字段，用于标识供应商是否与特定区域相关。
- DSP (Direct Supply Process): 直接供应流程，一种物料供应模式。
- 入场预约: 供应商或访客进入工厂前的预约管理流程。
- 访客管理: 对进入工厂的外部人员进行身份验证和权限管理的系统。
- 门禁管理: 控制人员和车辆进出工厂各区域的安全管理系统。
- 机房管理: 对工厂内重要设施机房的访问控制和管理。
- 通道口管理: 对工厂各个出入通道的管控和指引系统。
- 派单: 指系统根据预设流程或人工指令，自动生成并下发任务或订单。
- Passrate of linefeeding: 线边送料合格率，衡量物料准时、准确送达生产线的关键绩效指标 (KPI)。
- CI (Continuous Improvement): 持续改进。
- ROC (Return on Capital): 资本回报率，衡量公司资本使用效益的财务指标。
- Local EDW / Central EDW: 企业数据仓库的两种部署策略，分别为本地化部署和中央集中式部署。
- DOH (Days on Hand): 库存天数，衡量库存持有水平的指标。
- TO (Transfer Order): 转移订单，在仓库管理系统中用于记录和指导物料从一个存储位置移动到另一个位置的指令。
- R&R (Responsibility & Role): 职责与分工。
- Controller ID: 物料主数据中的一个字段，用于标识物料的物料需求计划（MRP）控制员。
- HC (Head Count): 人力/编制数量。
- dev.BZA: development BZA，一个用于开发或特殊流程的BZA（物料清单组件）版本，区别于主BZA。

### 2.2 生产与制造 (Production & Manufacturing)

- Backflush flag: 反冲标志，用于标识物料是否通过反冲方式消耗。
- BIW (Body In White): 白车身。
- BOM (Bill of Material): 物料清单。
- BORGR: SAP 系统中一种收货事务代码或流程，通常用于辅料收货。
- COGI: 尝试反冲时库存数量不足导致的系统报错。
- Consumption gap: 消耗差异，指实际消耗与系统记录之间的差异。
- EOL (End of Line): 生产线末端。
- Production Version: 生产版本，在 SAP 中定义了生产特定物料的不同方法，是 BOM 和工艺路线的组合。
- QM (Quality Management): 质量管理。
- WH customizing (Warehouse Customizing): 仓库定制，指在 SAP 系统中为满足特定仓库流程而进行的配置。
- 反冲: 生产完成后自动从库存中扣减物料的系统操作。
- 辅料 MM: 通过 MM（Materials Management，物料管理）模块进行管理的辅料。
- 爬坡阶段: 生产初期的产能逐步提升阶段。
- 退反冲: 撤销已执行的反冲操作，将物料重新计入库存。

### 2.3 项目管理与开发 (Project Management & Development)

- ALM Business Requirement 1000001044: 应用生命周期管理业务需求 1000001044，指“阻止超订单收货”的需求。
- Bphase: 车型开发阶段中的 B 阶段试装。
- BRD (Business Requirement Document): 业务需求文档。
- Cockpit: 管理驾驶舱，指用于监控和管理特定流程的集成界面。
- Demand Board: 需求委员会/需求评审会，用于评审和决定业务需求优先级。
- Dry-Run: 试运行测试。
- EH (EventHandel): 事件处理。
- EOP (End of Production): 生产结束。
- EP1 (Engineering Phase 1): 工程阶段 1。
- ET (Engineering Trial): 工程试装。
- ET1 (Engineering Trial 1): 第一次工程试装。
- Fit-Gap / Fit-Gap Analysis: 适配差距分析，用于评估系统需求与现有功能之间的差距。
- Hypercare: 超级关怀支持，指项目上线后的密集支持阶段。
- ITC (Integration Test Cycle): 集成测试周期。
- KEMs: Design Engineering Notification (设计工程通知) 或 Key Event Milestones (关键事件里程碑)。
- UAT (User Acceptance Test): 用户验收测试。
- LCM (Lifecycle Management): 生命周期管理。
- LCM (launch management): 项目启动管理团队，负责 EX33 等业务。
- LSO (Lots Specific Order): 试装零件的一种订购方式。
- Mandate: 项目或需求批准会议。
- NPOK (Not Process OK): 指生产或测试过程中不合格或失败的状态。
- NTP (New Type project): 新车型生产。
- PM Call (Project Management Call): 项目管理电话会议。
- POR (Production Order Release): 生产订单发布。
- PT1 / PT2: Production Trial 1 / 2，第一/二次生产试装。
- PT1B: PT1 的一个具体阶段或批次。
- SOP (Start of Production): 量产开始。
- PSI (Program Structure and Investment planning): 项目结构与投资规划，通常指年度或周期性的项目规划和预算审批流程。
- DB (Decision Board): 决策委员会，负责对重要事项（如项目、预算）进行评审和批准的机构。

### 2.4 财务与控制 (Finance & Controlling)

- IO (Internal Order): 内部订单。
- 序列化成本: 按车辆序列号分配的成本核算方式。
- Origin Group: 源组，一个由财务（CBFC）根据BZA信息映射生成的字段，用于内部管理，在AMS系统中不存在。

### 2.5 安全与合规 (Security & Compliance)

- 知其所需原则: 用户访问管理中的基本原则，确保用户只能访问其工作职责所需的系统和数据。
- 最小特权原则: 安全管理原则，用户应仅被授予完成其工作任务所必需的最低权限。

## 3. 代码与标识符 (Codes & Identifiers)

### 3.1 系统代码 (System Codes)

- 015326564B: BDA 编号，用于 V178 项目 Body Shop & Assembly。
- 101: SAP 系统中一种库存移动类型，通常表示收货入库。
- 942: SAP 系统中一种库存移动类型，通常表示收货暂存。
- ekpo-UEBTK: SAP 系统中采购订单行项目（EKPO）中的“无限超量交货”（UEBTK）字段。
- ESAM: 一个系统或流程代码。
- FBAC: 参考模式或系统名称。
- KSV: BDA 厂区测试系统, 德语 "Kunden-System-Vorbereitung"。
- KSW: SHU 厂区测试系统名称。
- M252: IPT 工厂发动机型号代码，用于 V178 项目发动机 call off。
- MET / OT2: 里程碑或测试阶段。
- PGSS: 一种 Creation Profile 类型，用于进口零件的 Call Off 策略。
- PL: Product Line or Part List, 产品线或零件清单。在本文档中特指为 One Box 项目定义的电池相关零件组。
- PLOC: 一种 Creation Profile 类型，用于国产零件的 Call Off 策略。
- PSV: 系统环境或阶段，特指用于测试和上线前准备的系统，例如用于仓库（WH）定制的上线。
- Variant: 指代特定产品或零件的不同版本或变体, 如 PL01, PL02。
- Z_DOHLT: SAP 中的一个表名，用于维护库存天数（Days on Hand）的限制。
- /DA0/0080_ROC_STOCK: SAP 中的一个自定义报表或事务代码，用于生成 RoC Stock Report。
- RoC Stock Report: 一份关于资本回报率（Return on Capital）相关库存的报告。

### 3.2 地点与区域 (Locations & Areas)

- 1046_640: MRP 区域代码，Assembly。
- 104_AF: 试装厂区,MRP 区域分配代码，用于 V178 项目爬坡阶段。
- 104_AS4: MFA 厂区总装,MRP 区域分配代码，用于 V178 项目车辆生产。
- 104_AS4J0/J1/J2: MFA 厂区总装 JIS,MRP 区域分配代码。
- 104_BA1: 电池项目对应的 MRP 区域。
- 104_BS1/BS2/BS4: MRA1/MRA2/MFA 装焊。
- 104_EN1: MRP 区域分配代码，用于 eATS 项目。
- 12R: Ramp Up Warehouse Central 收货区域代码。
- BDA: 北京奔驰汽车有限公司 (在此也作为地点上下文使用)。
- EI11X: ESAM EP1 Interim 卸货点代码。
- HAF1: 仓库区域代码。
- HTY1: 存储位置代码，TY AS (Trailer Yard Assembly Shop)。
- MFA: 前驱车工厂。
- MRA1: 物理位置 MRA1。
- SHU: 顺义工厂。
- Storage Location: 存储地点。
- T4A3: 卸货点代码，TY AS USA (Assembly parts from USA)。
- UAFK: 电池项目零件存储位置。
- UAFL: Ramp-Up EP1 存储位置代码。

### 3.3 车型与项目 (Models & Projects)

- 243 车型: 指奔驰 A 级车。
- Compact / Large: 指 eATS 的规格。
- EX33: 辅料采购订单类型或相关业务流程代码。负责此业务的团队是 LCM (launch management)。
- GLC: 车型系列名称。
- MY251 (Model Year 2025.1): 2025 年第一季度车型年。
- One Box: 一个涉及将电池和相关组件打包处理的项目。
- T167, V174, V178, V206, V520, V530, V540: 车型代号。
- WP1-7: AmSupply 项目中的不同工作包 (Work Package)。
- MMA (Mercedes-Benz Modular Architecture): 车型平台项目代号，代表中小型车，如 A 级、CLA。
- MBEAM: 车型平台代号。中大型车,例如 C 级,GLC.
- GW: Gateway，可能指项目中的某个关键节点或阶段。
- MD: Master Data 或其他缩写，在此上下文中指代测试设置相关方。
- VP & BP: Vehicle Plant & Battery Plant, 整车工厂和电池工厂。
- 清线: 生产线清空操作，指将生产线上的特定产品（如A电池V174车辆）全部生产完毕，为切换到新产品做准备。
- 硬切换: 在生产中从一种产品配置（如A电池）直接切换到另一种配置（如PL电池），不存在并行生产阶段。
- DMC (Document Management Center): 文档管理中心，用于管理产品相关文档和数据。

### 3.4 交付与订单类型 (Delivery & Order Types)

- ZLEC, ZLRR: SAP 系统中的交付类型(Delivery Type)，用于空容器退回流程。
- ZLSA: SAP 系统中的销售订单的交付类型(Delivery Type)。
- ZLSS: SAP 系统中的交付类型(Delivery Type)，用于 JIS BUY&SELL 流程。

## 4. 组织与角色 (Organizations & Roles)

- BBAC (Beijing Benz Automotive Co., Ltd.): 北京奔驰汽车有限公司。
- Benteler: 供应商名称。
- BU (Business Unit): 业务单元。
- CoC: Central Operations Control, 德国总部的中央运营控制团队，也常指代德国IT相关支持团队。
- COC Level 2 team: CoC 团队的二级支持团队。
- GSS (Global Supplier Services): 全球供应商服务。
- Information Owner: 信息所有者，在账号权限申请流程中负责审批的角色。
- IT (Information Technology): 信息技术。
- L2/E2: 公司岗位第二级, 高级经理。
- L3/E3: 公司岗位第三级, 总监。
- L4/E4: 公司岗位第四级, 经理。
- L5/E5: 公司岗位第五级, 组长。
- Log PL (Logistics Project Leader): 物流项目负责人。
- MBAG: 梅赛德斯-奔驰集团 (Mercedes-Benz AG)。
- MBFS (Mercedes-Benz Financial Services): 梅赛德斯-奔驰金融服务。
- RD (Research and Development): 研发部门。
- TUS: 美国 Tuscaloosa 工厂。
- UAM (User Access Management): 用户访问管理。
- ZF: 供应商名称。
- MP (Mercedes-Benz Procurement): 梅赛德斯-奔驰采购部门。
- IPS (International Procurement Services): 国际采购服务。
- Hella: 供应商名称，如 Hella Shanghai Electronics Co.,Ltd.。
- SB Schenker: 辛克，物流服务提供商，为BBAC开发ATM系统。
- PTO (Product Team Owner): 产品团队负责人，负责特定产品线或功能模块的整体管理和协调。
- BBAC WP Lead (BBAC Work Package Lead): 北京奔驰工作包负责人，负责特定工作包在本地的执行和管理。
- SMT LOG (System and Method Team Logistics): 系统与方法团队物流组，负责物流相关的系统和方法论支持。
- MRP Product Team: 物料需求计划产品团队，负责MRP相关功能的开发和维护。
- JIS Product Team: 按序供应产品团队，负责JIS相关功能的开发和维护。
- DECO Product Team: DECO产品团队，负责DECO相关功能的开发和维护。
