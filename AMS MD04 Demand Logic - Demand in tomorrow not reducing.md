﻿---
title: "AMS MD04 Demand Logic - Demand in tomorrow not reducing"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
---
title: "AMS MD04 Demand Logic - Demand in tomorrow not reducing"
summary: "AMS系统MD04需求逻辑问题，明日需求未减少的分析和解决方案"
date: "2021-11-23"
updated: "2022-03-11"
status: "completed"
tags:
  - "AMS"
  - "MD04"
  - "需求逻辑"
  - "超产"
category: "Issue"
project: "AMS系统优化"
---

# AMS MD04 Demand Logic - Demand in tomorrow not reducing

‍

## ⭐️ Key

[📅20211123 email David <PERSON>，准备安排PM Callimage.png](#20220311132625-wafb1c0)

## 📆 Storyline

📅20211123 email David <PERSON>，准备安排PM Call![image.png](assets/image-20220311132646-irzwk4o.png)

📅202112161319 Ticket 0059794523

📅202201171424 同WP2会议后, 目前ticket端让创建DRF, 因为觉得问题是超产造成的而不是系统. 在==同天PM Call==上同[Markus](CRM/Markus%20Egeler.md)沟通后, 会组织小会议讨论.

📅202201201051 不光是超产造成的, 系统需求靠后, ASF平均打散

📅202201241705 Florian答复, AMS Product Team建议不要超产.

📅202202150907 建议pending到logic改变的需求后面

📅20220311 在WP2同MRP的会议上讨论得到几轮,  但[product team的答复](#20220311134500-55micko)仍有效, 需要具体的案例支撑上诉.

[📅20231018 补充, 根据记忆, 最后没有切换回IPT逻辑, 因为IT说没有发现技术风险但不能保证100%没问题, 用户MRP要求IT保证完全不会出问题否则不切回IPT逻辑. 最后就一直...](.md#20231018151506-vte1px4)

## 🔗 Reference

/  
/

