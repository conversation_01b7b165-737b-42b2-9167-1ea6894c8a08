﻿---
title: "Training.操作文档 - 查询零件所用车型、车号、用量 - ZPCVBMAT"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 操作文档 - 查询零件所用车型、车号、用量 - ZPCVBMAT

[操作文档 - 查询零件所用车型、车号、用量 - ZPCVBMAT (bbac.local)](http://team.bbac.local/Sites/LDM/WIKI/D/ZPCVBMAT/index.html)

‍

> 用于查询零件车型、车号和单车用量信息。也可反向通过车号查询BOM清单或上线时间。

# 操作步骤

## 数据准备

需要提前获取车辆的`Production Number`, 或是`零件号`.

## ZPCVBMAT

T-code: `SQ00`

### 调整Environment

> 点击上方菜单栏中的`Environment`, 选择`Query Area`
>
> ![image.png](assets/image-20220415152540-bu537zh.png)
>
> 选中`Standard Area`后点击确定按钮![image.png](assets/image-20220415152638-uohqw0s.png)
>
> ![image.png](assets/image-20220415152552-zep1y1u.png)

### 调整Group

> 点击![image.png](assets/image-20220415152752-ykof42d.png), 或者使用快捷键`Shift+F7`, 之后双击选择User Group = WP2.
>
> ![image.png](assets/image-20220415152739-ad8oa7t.png)

‍

### 打开Query

找到Query `ZPCVBMAT`,选中该行后点击![image.png](assets/image-20220610085549-rng359k.png)(F8)执行。

![image.png](assets/image-20220610084751-brd0hn7.png)

## 设置配置参数

以下参数请根据实际需要选填。

`CC Production Number`为车辆生产号。

在不明确车辆生产号的时候，建议限制`Current Date of Application`日期数据，避免数据量过大。

`DIALOG Part Number`和`Material Number`代表两种不同的零件格式，区别参考图例。

❗带ES码的零件仅可使用在`DIALOG Part Number`位置, 格式参考`A2476909701*9051`

`FLO - Production Line`可以选定生产线, 例如`ASSY*`为总装, `BODY*`为装焊。 未选择的情况下, 一般车辆会在结果页面分别显示其预计装焊上线和总装上线时间。

![image.png](assets/image-20220610085709-svuq901.png)

点击![image.png](assets/image-20220610085549-rng359k.png)(F8)执行。

## Report

在结果界面中可以看到车辆信息，时间，零件号，车型，用量，Receiver Group以及BOM ID等信息。

![image.png](assets/image-20220610085536-rutau9a.png)

# 常见问题解答

带ES码的零件仅可使用在`DIALOG Part Number`位置, 格式参考`A2476909701*9051`

# 文档信息

> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>
> 初次发布时间：2022-01-25
>
> 最后更新时间：2022-06-13
>
> 版本: V1.4

‍

