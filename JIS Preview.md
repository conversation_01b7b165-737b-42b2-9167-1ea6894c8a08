﻿---
title: "JIS Preview"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "JIS"

category: "Project"
project: "JIS系统"
---
# JIS Preview

# JIS Preview 閫昏緫鍙樺姩

JIS preview 鍦↖PT涓粠BS寮€濮嬪喕缁? 鍦ˋMS涓粠AS寮€濮?IB鐐?鍐荤粨, 浜嶫IS-A鐨勫彂閫佹椂闂翠竴鑷?

JIS Actual 鍙細鍙戦€佷竴娆?

## Change based in AMS

> JIS Preview is sending only one time for each car, only there is a Bom change or assembly start date change, then it will send the second time.

鍥燗MS鐨凧IS-Preview閫昏緫鍙樻洿, 榛樿閲囧彇change based妯″紡, 涓斿綋鏃ュ唴鍙樺姩涓嶈涓篶hange, 瀵归儴鍒嗕緵搴斿晢(Brose)閫犳垚浜嗗洶鎵?
鍥犳浠嶴HU鏉ラ渶姹? 杩涜璋冩暣 [绛斿__绛斿__JIS渚涘簲鍟嗘帴鏀禼all_off闂.msg](assets/绛斿__绛斿__JIS渚涘簲鍟嗘帴鏀禼all_off闂-20210622151236-z5f5fgp.msg)

缁忚繃COC妫€鏌? 绯荤粺涓繘琛屽涓嬫洿鏀? 鍗冲彲杩涜寮哄埗鐨刦ull JIS call

![image.jpg](assets/20210120201153-1u4sftf-image-20210409103650-46yqub7.jpg)

![image.jpg](assets/20210120201218-e6n1npk-image-20210409103653-zmb6n5u.jpg)

閫夋嫨 `complete transmission - continuously`

`Re_JIS_BOM 鍐荤粨鏈熼棶棰?20210415135331-tc5xwl2.eml`

`鍖椾含濂旈┌澶у叴宸ュ巶鐗╂祦绯荤粺鍗囩骇鍙婂叾瀵?JIS-P 鐨勫彉鍔ㄤ粙缁峘

## MFA AMS 涓婄嚎鍓嶅悓 JIS 渚涘簲鍟嗘矡閫氳褰?- 鍒橀敠鍗?
`20211110 绛斿 JIS 渚涘簲鍟嗘矡閫氬強绯荤粺娴嬭瘯璁板綍.msg`

鈥?

