# 20221011

# Source List MRP <> 2 block schedule line

link: [source valid date 影响](Projects/AmS@BDA/AMS.BDA.Training/AMS.问题解决/Quota.md)

📅20221011

咨询下source list （ME01）中取消MRP=2的负面作用。

IPT中进行零件EOP/ECC控制的时候一遍选择勾选blk来阻止订单生成。

AMS中因为这样做会影响收货，所以没有好的控制方法。

最近经MRP反馈，在KSV中测试后发现，取消2的设置会达到相同的阻止订单生成的效果。

这样做有其他负面影响吗，会影响收货吗？

![image](assets/image-20221011135101-xts2h4o.png)

![image](assets/image-20221011135106-ptwt7y0.png)

📅20221011 吴立新答复没有其他影响，已经告知MRP [zip](es://20221011%20RE%202%20in%20Source%20list_20221011_023238.zip)

📅20250604

| 情况   | Source 状态 (日期范围) | MRP Relevant 取值 | SA 状态 (日期范围) | AMS 逻辑中的行为 (是否生成Schedule Line) | HANA 逻辑中的行为 (是否生成Schedule Line) | HANA 逻辑解释                                                                                                | 潜在影响           |
| :----- | :----------------------- | :---------------- | :----------------- | :--------------------------------------- | :---------------------------------------- | :----------------------------------------------------------------------------------------------------------- | :----------------- |
| 情况1  | 范围外 (Source 失效)     | 空                | 生效               | 不会生成 Schedule Line                    | 会生成 Schedule Line                       | Source已经失效。HANA 在没有找到有效 Source 的情况下，会主动去找有效的 SA。因为该 SA 的日期仍是生效的，所以生成了 Schedule Line。 |                    |
| 情况2  | 范围内 (Source 生效)     | 空                | (未明确提及)       | (未明确提及)                             | 不会生成 Schedule Line                       | Source 生效，但是与 MRP 无关。                                                                                | 无                 |
| 情况3  | 范围内 (Source 生效)     | 2                 | 范围外 (SA 失效)   | (未明确提及)                             | 会生成 Schedule Line                       | Source 生效且与 MRP 相关。但是 SA 的日期调整到范围外，表示 SA 失效。HANA 仍会生成 Schedule Line。                      | 影响未知 (可能无法收货) |

推荐控制方法:
场景示例: 希望系统在某年7月1日前生成Schedule Line，从当年7月1日开始则不生成Schedule Line。
推荐配置方法 (使用两条Source记录):
 Source记录 1 (确保目标日期前生成):
 Valid from: (例如: 当前或更早日期)
 Valid to/end:当年6月30日
MRP Relevant: "2" (或其它表示MRP相关的值)
说明: 此记录确保在有效期内 (直至6月30日)，Schedule Line 会被创建。
Source记录 2 (确保目标日期后不生成):
 Valid from:当年7月1日
Valid to/end: 9999年12月31日 (或一个非常遥远的未来日期)
 MRP Relevant: "" (空值，表示与MRP无关)
说明: 从7月1日开始，此记录生效。由于其MRP Relevant为空，系统将不会为此Source生成Schedule Line。HANA系统会根据日期及其他优先级规则选择最合适的有效Source记录。

# Call Off 无法正常发出

📅20221011

email吴立新：目前进口国产都发现有部分零件的call off无法正常发出

举例 A 167 900 04 31

![634507efe4b070b1b7238955](assets/634507efe4b070b1b7238955-20221011141145-ggio5kh.png)

![634507efe4b070b1b7238956](assets/634507efe4b070b1b7238956-20221011141243-tfk0d7r.png)

![634507efe4b070b1b7238957](assets/634507efe4b070b1b7238957-20221011141247-i0vtd13.png)

Simple IT Request: 6100002247, Call Off Stuck for Partial parts

> Hello
>
> We found the reason for the “Left-Over” entries in the ME9E:
>
> The cyclic release job only covers releases of the actual day. In case, there are left-overs from previous days, they will not be automatically processed. Therefore, one additional job is required that covers all open ME9E entries and that will run once per day. Martin will create this additional job tonight.
>
> Regarding the EX33s: They will never be released by the ME9E job (cyclic release), they need to be processed manually. If you want to, it can be added to the cyclic release job and then also be processed automatically. Please let us know if you want that.
>
> Regards
>
> Christoph

‍
