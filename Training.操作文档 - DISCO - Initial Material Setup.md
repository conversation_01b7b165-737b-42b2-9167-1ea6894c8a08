﻿---
title: "Training.操作文档 - DISCO - Initial Material Setup"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 操作文档 - DISCO - Initial Material Setup

# 常见问题解答

## 系统创建任务的逻辑

1. 筛选车辆Model-> TABLE = /DA0/MMIC_FIL, 相关零件分别传入IPT和AMS![image.png](assets/image-20211001134054-ulhwwfa.png)
2. 筛选BZA-> TABLE = /DA0/MMIC_UBZA

## 系统识别默认MRP Area逻辑

[DISCO如何识别MRP区域 How DISCO Recognize MRP Areas.pptx](es://DISCO如何识别MRP区域%20How%20DISCO%20Recognize%20MRP%20Areas.pptx)

1. Dialog -> 显示零件车型 ：[/DCI/RQI_MB_MASTER，查看零件DIALOG信息](Projects/AmS@BDA/蓝图%20&%20流程/AmS%20BDA%20T-Code%20List.md#20230220110126-w3zs7a0)
2. 车型 -> 识别Prod Hall ：Table [/DA0/0090_PRHALL](Projects/AmS@BDA/数据获取/Table%20SE16N.md#20221014084810-2ym7by9) 或者 Query WP3_MIG-WP3_0016
3. Prod Hall -> 识别MRP area：Table [/DA0/0090_HBERID](Projects/AmS@BDA/数据获取/Table%20SE16N.md#20221014084726-4ifkycv) 或者 Query WP3_MIG-WP3_0016

## 如何修改MRP Controller

零件在DISCO IMS中状态到200以上时，如需要修改MRP Controller，请按如下步骤：

![](assets/clip_image001-20221110162316-lviwwm8.png)

1. 零件还未激活（900）， 可删除任务重新开始，或是等待激活后再按照下面的步骤修改。
2. 零件已经激活（900）：

MM02 ： 初次使用时请选择104, MRP1，然后进入如下界面

![image](assets/image-20221110162327-dvnjk2q.png)

1. 修改MRP Controller
2. 点击MRP Area
3. 逐一双击进入每个MRP Area
4. 修改MRP Area下的Controller
5. 点击Copy保存
6. 点击Copy保存
7. 点击保存图标保存

==需要特别注意==的是修改后，如果:

BIW改为AS，需要: 移除AMSR标识

AS/BS 共享件:
1 检查物料主数据工厂级别MRP controller 是BS 还是AS
2 查Dialog 数据，找出那些与工厂级别属性不一样的model Part ID ,工厂级别定义的是BS, 找出AS对应的Model ID
3 通知Docu 加上Deviating MRP controller

AS改为BIW，MM02任意更改这个“Plant-sp.matl status”的值，保存，然后再MM02改回40，保存。系统晚上会自动更新需求，第二天就自动添加上AMSR。
如果着急需要联系SAP Helpdesk，让其通过/DA0/4010_F61推送AMSR给DIALOG。但TBE反正隔天才会run

‍

## 激活失败 - Function "Send zero release": error creating forecast del.schedule

在 900 步骤激活时失败, 查看 log 提示:![image.png](assets/image-20211029141349-llz4fg4.png)

新供应商信息在 WE20 中需要进行初始配置, 联系 Local IT.

之后重新触发激活.

## 激活时各MRP Area下Controller ID的变化

![202212121540](assets/202212121540-20221212154136-og4pzjr.jpg)

## 双货源无法获取第二个供应商信息

双货源零件在完成200步骤后直接变更为300国产货源，没有M标记供手工选择供应商，检查合同发现，进口端GSS已经完成但是ProQ无反馈。

经查这种情况是在国产合同生效的时候，ProQ在收到进口合同信息后自动调整到了国产合同上，导致进口合同无法生成，需要联系相应的采购员进行系统调整。

[答复 A1679004831_20221215_094632.zip](es://答复%20A1679004831_20221215_094632.zip)

‍

