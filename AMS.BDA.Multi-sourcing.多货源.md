﻿---
title: "AMS.BDA.Multi-sourcing.多货源"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# AMS.BDA.Multi-sourcing.多货源

- [AMS.BDA.Multi-sourcing.多货源](#amsbdamulti-sourcing多货源)
  - [Summary](#summary)
  - [Storyline](#storyline)
  - [子主题与细节](#子主题与细节)
    - [采购部门关于多货源方案的咨询 (2025)](#采购部门关于多货源方案的咨询-2025)
      - [Summary](#summary-1)
      - [Storyline](#storyline-1)
      - [Reference](#reference)
      - [会议纪要 (2025-04-14)](#会议纪要-2025-04-14)
        - [1. 背景与目标](#1-背景与目标)
        - [2. 技术现状与挑战](#2-技术现状与挑战)
        - [3. 跨部门协作需求](#3-跨部门协作需求)
        - [4. 风险评估与建议](#4-风险评估与建议)
        - [5. 典型案例讨论](#5-典型案例讨论)
        - [6. 后续计划](#6-后续计划)
    - [JIS 按配额比例多货源的尝试 (2020-2023)](#jis-按配额比例多货源的尝试-2020-2023)
      - [Summary](#summary-2)
      - [Storyline](#storyline-2)
      - [Reference](#reference-1)
    - [买卖件(Buy Back)功能强化需求 (2020-2021)](#买卖件buy-back功能强化需求-2020-2021)
      - [Summary](#summary-3)
      - [Storyline](#storyline-3)
      - [Reference](#reference-2)
    - [背景与定义](#背景与定义)
      - [Summary](#summary-4)
      - [Storyline (2021年度重要事件摘录)](#storyline-2021年度重要事件摘录)
      - [Reference](#reference-3)
    - [系统操作指南：多货源维护](#系统操作指南多货源维护)
      - [Summary](#summary-5)
      - [通过供应商变更 (Change Vendor) 实现多货源](#通过供应商变更-change-vendor-实现多货源)
      - [通过变更卸货点 (Change of Unloading Point) 调整 Item 行 ULP](#通过变更卸货点-change-of-unloading-point-调整-item-行-ulp)
      - [JIS 特殊配置：激活拆分并设置RG](#jis-特殊配置激活拆分并设置rg)
    - [JIS 按车型拆分多货源实现方案 (Car Model Based)](#jis-按车型拆分多货源实现方案-car-model-based)
      - [Summary](#summary-6)
      - [Reference](#reference-4)
    - [买卖件（进口/国产）多货源主数据维护规则](#买卖件进口国产多货源主数据维护规则)
      - [Summary](#summary-7)
    - [JIS Receiver Group](#jis-receiver-group)
      - [Summary](#summary-8)
      - [Storyline](#storyline-4)
      - [Reference](#reference-5)
    - [JIT Multi-sourcing by Quota Order](#jit-multi-sourcing-by-quota-order)
      - [Summary](#summary-9)
      - [Storyline](#storyline-5)
      - [Reference](#reference-6)
    - [JIT Multi-sourcing within a Single Car Model (Gap)](#jit-multi-sourcing-within-a-single-car-model-gap)
      - [Summary](#summary-10)
      - [Storyline](#storyline-6)
      - [Reference](#reference-7)
    - [V214/V206 Multi-sourcing Issues](#v214v206-multi-sourcing-issues)
      - [Summary](#summary-11)
      - [Storyline](#storyline-7)
      - [Reference](#reference-8)
    - [V213/V214 Multi-sourcing (Reference Only)](#v213v214-multi-sourcing-reference-only)
      - [Reference](#reference-9)
    - [《多货源系统支持总结》](#多货源系统支持总结)
      - [Summary](#summary-12)
      - [一、IT系统支持多货源的基础](#一it系统支持多货源的基础)
      - [二、IT系统已支持的7种多货源形式](#二it系统已支持的7种多货源形式)
      - [三、不同多货源形式的具体情况](#三不同多货源形式的具体情况)
        - [案例分析：48V 电池供应切换 (2024)](#案例分析48v-电池供应切换-2024)
          - [Summary](#summary-13)
          - [Storyline](#storyline-8)
          - [Reference](#reference-10)
      - [四、建议方案](#四建议方案)
  - [Reference (Overall)](#reference-overall)

## Summary

本文档核心围绕 AMS (AmSupply) 系统在 BDA (Beijing Benz Automotive) 环境下处理 JIT (Just-In-Time) 和 JIS (Just-In-Sequence) 零件多货源供应策略的挑战、讨论过程、解决方案及相关背景信息。主要目标是在现有系统框架内，有效管理同一零件来自不同供应商的需求分配，特别是在涉及单一车型或生产线内多货源的复杂场景。

关键进展与结论:

- JIS 多货源:
  - 主要通过 DIALOG 系统中的 Receiver Group (RG) 字段 (RG1/RG9) 实现，用于区分不同供应商或位置/模块。RG 维护需在供应商变更前完成，替代 IPT 中的 BTID 作用。需注意 RG9 与装焊 AMSR 字段的潜在冲突。
  - 按配额比例 (Quota) 的 JIS 多货源需求因系统底层逻辑复杂、开发量巨大，已被 COC 明确拒绝开发。虽然业务部门后续仍有提及，但系统层面不支持。
  - 按车型拆分的多货源通过维护 Receiver Group 实现，清单需与 RD-BVA 同步。
- JIT 多货源:
  - Quota Order: 确认是业务必需功能，即使曾考虑延后。
  - 单车型内 JIT 多货源 (Gap): AMS 系统本身无法像 IPT 那样精确区分同一车型内不同供应商的需求。
    - 解决方案 (V213 Hybrid): 通过为特定零件添加 ES1 代码并修改 Baukasten BOM 结构，使 DIALOG 能区分需求，已通过 KSV 测试并实施。
    - 案例 (V214/V206): 经确认零件可在生产线混用，无需强制区分订单。决定待国产化切换时间点确定后统一供应商。
  - JIT/JIT 相同零件号: 系统通过 Quota 支持待验证，暂无业务场景，建议手工控制。
  - JIT/JIT 不同零件号 (互选件): 发动机工厂有 IPT 方案，其他工厂基于 Baukasten Generator 的方案待验证，整车厂暂不支持。
- 买卖件 (Buy Back): 曾有功能强化需求（虚拟库存、自动排程），但因库存盘点和财务影响复杂，评估后确认系统无法按原需求实现。
- 主数据维护:
  - 首次 SA 创建需遵守零件采购类型 (BZA)。
  - 多货源通常通过 `Change Vendor` 添加新 SA/Vendor，并维护 MRP Area。
  - JIS 零件需在 MM02 中维护对应的 JIS Receiver Group。
- 采购部门咨询 (2025): 探讨了现有系统在不进行新开发的前提下的多货源可能性，特别关注 Quota 功能。

系统支持总结:

- IT 系统支持多货源的基础是维护不同的 Receiver Group 和 MRP Area。
- 已识别并实例验证了 7 种 IT 系统支持的多货源形式（涉及 JIS/JIS, JIS/JIT, JIT/JIT 等组合，跨车型、跨产线等场景）。
- 建议优先利用已支持的形式实施高价值零件的多货源。

## Storyline

- [2020-08-20] MRP 部门 (郝学硕) 提出 JIS 零件按配额比例供货的需求。
- [2020-08-24] 物流部门 (刘锦华) 建议 BU 先明确需求，MRP 需与 JIS 规划协商可行性。
- [2020-09-02] 刘锦华反馈 COC 认为 JIS 按配额供货难度大，涉及底层逻辑。
- [2020-09-09] 刘锦华邮件答复 COC 不会开发 JIS 按配额供货功能。
- [2020-09-09] 与 Ruestem 讨论后，建议将 JIS 配额需求放入 SFM 与 Christian Benz 讨论，邮件发送给 MRP 未获答复。
- [2020-10-26] 确认 JIS 按车型拆分是通过 DIALOG receivers 实现的。
- [2020-12-07] JIT 单车型多货源 Gap 在 PM Meeting 上由 WP2 展示。
- [2020-12-16] Florian Schneider 介绍 JIT 单车型多货源替代方案：RD 设置新的 ES1 零件号。
- [2020-12-22] 张同指出 BBAC RD 无法直接添加 ES1 code，需 BTV 释放 KEM。
- [2020-12-23] 张辉向 Gao Po 解释 V213 汽油/混动车多货源原因。
- [2020-12-xx] 收到业务部门关于 Buyback 零件订购支持的 BRD 及流程描述。
- [2021-01-05] Gao Po 回复可考虑为 V213 混动增加 deviation L8。
- [2021-01-05] Florian Schneider 会议讨论 JIT 单车型多货源方案：1) 新零件号 (ES1) 2) 改为 JIS (难度大)。
- [2021-01-06] Daimler RD 答复可以进行 ES 码操作，需联动采购测试。
- [2021-01-13] 与 JIS 规划蒋文娟确认 GXX 蓝图细节。
- [2021-01-13] Globus 当前无法处理 ES1，需等 ProQ 更新 (预计 4 月测试)。
- [2021-01-xx] 进行 Buyback 功能技术可行性讨论。
- [2021-01-29] 内部讨论或记录显示确认 JIT Quota Order 功能是业务必需的。
- [2021-03-xx] 评估确认 Buyback 功能无法按原需求实现。
- [2021-04-14] WP1 咨询能否去除 BGI1，被告知需等待 ProQ 测试结果。
- [2021-04-30] Oliver Rohatsch 表示 ES1 是 ProQ 标准功能，无需测试。
- [2021-05-06] 与研发 Dennis 讨论需求，确认 24 个 MFA 多货源零件实施方案。
- [2021-07-01] 确认 JIS/JIT 多货源数据迁移规则：必须先维护 JIT 货源。
- [2021-11-03] 关键确认：DIALOG 中 Receiver Field (RG9) 可用。JIS 列表由 JIS Planning 定义，与 BVA 确认，RDC 录入。
- [2022-02-18] 准备 MRA SA Validation List 时发现 JIT 单车型多货源问题仍存，邮件 Oliver 提出需处理。
- [2022-03-30] 宋庆庆反馈 V213 两个多货源零件的 Header 号不同，为区分提供基础。
- [2022-04-06] 准备 JIT 单车型多货源问题会议 PPT。
- [2022-04-13] RD Gao Po 初步答复不能添加零件号。
- [2022-04-15] 与胡青平 (RD) 开会确认不能添加零件号。
- [2022-04-19] WP2, WP6, 胡青平会议，安排检查 ES1 和 manual control 方案。
- [2022-04-26] 与 RD 第二次会议，讨论 ES1 Key 和 Call off ZB from Gestamp 方案。
- [2022-05-10] 与胡青平、宋庆庆会议，否定方案 2，继续关注 ES1 方案。
- [2022-05-13] Dokhaus 会议，赵新宇介绍 ES1 不在 BOM 中，DIALOG 无法拆分。
- [2022-05-16] 与 Florian, Dennis, Stefanie 会议确认可通过修改 V213 Hybrid Baukasten BOM 添加 ES1 Key 控制。
- [2022-05-27] KSV 测试成功，确认 Hybrid 零件号变更。与采购、Controller 沟通潜在问题。
- [2022-06-23] V213 ES1 方案合同完成。
- [2022-08-16] 与李建群沟通 V213 ES1 切换细节，确认需求无问题，无需提前下单，供应商处理 ASN，驻场支持贴签。
- [2022-08-26] MRP 咨询 V206/V214 JIT/JIT 订单拆分问题。
- [2022-08-31] 整理 V206/V214 涉及的零件清单。
- [2022-09-01] Florian 答复 AMS 无法实现 V206/V214 场景的区分。
- [2022-09-06] 在 PM Call 上展示 V206/V214 问题，向 RD 提出疑问。
- [2022-09-14] 与 MMT, Christian Reuter 会议，决定在 V214 项目会上提出，要求 SOP 前统一 Vendor。
- [2022-09-22] V214 项目组专项会议，何红 (RD) 确认零件在 BBAC 生产线可混用，无需分开订购。提供 PS 接口人董静咨询切换事宜。
- [2022-10-03] 在 DISCO 中激活 V213 新 ES1 零件号合同，删除旧号安全库存。MRP run 后需求正常。
- [2022-11-22] V206/V214 多货源议题关闭。
- [2023-05-10] 计划与 RD 重新开会，对齐 JIS Receiver Group (RG) 维护流程。
- [2023-05-15] 与房旭、郑思闽、蒋文娟会议，讨论采用 "J + 供应商代码后三位" 作为 Receiver 格式的可能性。
- [2023-05-29] 与赵新宇、姚琼、苑英楠会议，介绍 JIS Receiver。聚焦房旭负责的 loop。
- [2023-06-02] 咨询房旭，下周将组织会议讨论 loop 事宜。
- [2023-08-22] 采购部门（黄晨男，吴立新，刘星）再次提及 JIS 按配额供货需求。
- [2025-01-23] 收到采购部门任务，与 Local IT 开会沟通多货源解决方案。
- [2025-02-10] 向 Christoph Muench 发送邮件，请求其对 Quota 功能进行讲解。
- [2025-04-14] 与MRP讨论双货源模式（尤其是相同零件号 JIT/JIT）的技术可行性、手工操作风险（物流、财务、质量追溯）以及跨部门协作需求。明确了采购需主导公司级项目，并计划后续进行风险模拟和技术调研。
-

## 子主题与细节

### 采购部门关于多货源方案的咨询 (2025)

#### Summary

采购部门 (P&S) 提出关于现有系统支持多货源方案的咨询，旨在不进行新开发(BRD)的前提下，探讨现有系统的可能性。特别关注配额(Quota)功能的使用，需 CoC 专家 (Christoph Muench) 支持进行讲解。

#### Storyline

![alt text](assets/image.png)

- [2025-01-23] 收到采购部门任务，与 Local IT 开会沟通多货源解决方案。
- [2025-02-10] 向 Christoph Muench 发送邮件，请求其对 Quota 功能进行讲解。

#### Reference

- 文件: [PossibleMultiSourcing_20250123.pptx](es://PossibleMultiSourcing_20250123.pptx)
- 会议截图: ![image](assets/image-20250210165734-j37bhso.png)
- 相关人员: 刘俊, 吴立新, [fabian_marcel.kamilli](CRM/fabian_marcel.kamilli.md), Christoph Muench

#### 会议纪要 (2025-04-14)

本次会议围绕多货源技术方案（尤其是双货源模式）的可行性、手工方案风险及跨部门协作需求展开，讨论要点如下：

---

##### 1. 背景与目标

• 需求背景：采购部门希望推动“双货源”模式（同一零件号、相同生产线/车型/位置引入多个供应商），以应对供应链风险并降低成本，但当前系统（如AmSupply）缺乏直接支持。
• 核心矛盾：IT系统对JIT&JIT/JIS&JIS等双货源形式的支持有限，需依赖手工方案，但存在跨部门协同和操作风险。

---

##### 2. 技术现状与挑战

• 系统支持情况（基于本文档《多货源系统支持总结》部分）：
  • 已支持场景：系统支持7种多货源形式（如不同生产线/车型/装配位置的JIS&JIT组合），但相同零件号的JIT&JIT/JIS&JIS需手工干预。
  • 待验证功能：AmSupply的Quota功能对JIT&JIT的支持未经验证，整车厂互选件方案尚未落地。
• 手工方案局限性：
  • 物流库存失衡与供应中断： 依赖人工控制库存比例（如先进先出规则），难以精确管理，极易导致库存积压或关键零件供应中断。
  • 财务核算困难与成本失真： 财务系统无法通过反冲数据区分来自不同供应商（例如，A供应商单价100元，B供应商单价10元）的相同零件成本，需要大量手动调整，导致单车成本计算不准确。该数据不影响付款(收货时付款可以区分供应商), 但是影响HPV的计算(因为不知道实际装车的零件供应商)
  • 质量追溯困难与安全隐患： 相同零件号在系统中无法关联到具体的供应商批次，一旦出现质量问题，难以快速准确追溯来源，特别是对于安全件，存在严重的安全隐患。

---

##### 3. 跨部门协作需求

• 涉及部门：物流、采购、财务、质量、生产、规划等。
• 关键协作问题：
  • 采购主导：需采购牵头成立公司级大项目，协调各部门分析方案可行性（如本文档中记录的48V电池案例）。
  • 实施周期：从方案验证到落地需至少1年，需德国IT团队支持，但采购当前无明确计划。
  • 优先级冲突：部分场景（如进口与国产切换）因成本或技术限制难以快速实施。

---

##### 4. 风险评估与建议

• 手工方案风险：
  • 灵活性不足与响应滞后： 当生产计划或供应商配额（如从50%调整到90%）需要动态调整时，手工操作无法及时、准确地响应，影响生产效率和计划执行。
  • 部门责任模糊与额外负担： 手工方案增加了物流、质量、财务等多个部门的额外工作量和协调难度，容易出现责任不清、效率低下的问题。
• 建议措施：
  • 短期：参考现有轮胎JIS方案，由Tier1供应商控制Tier2货源比例。
  • 长期：推动系统功能优化（如Quota模块验证），需业务部门评估成本（本文档《多货源系统支持总结》中提及总成本约204万欧元）。

---

##### 5. 典型案例讨论

• 48V电池国产化：曾尝试双货源，但因系统限制切换为单一货源，暴露手工方案在动态调整中的不足 (见本文档案例分析)。
• 发动机互选件：动力总成工厂已实现不同零件号的JIT&JIT，但整车厂缺乏支持基础。

---

##### 6. 后续计划

• 深入评估：物流部门联合采购、质量、财务等，针对具体场景（如MRA2生产线）模拟手工方案风险。
• 技术调研：与德国IT团队沟通系统支持可能性，明确开发周期与成本。
• 报告输出：向高层提交风险评估，推动采购决策是否立项。

### JIS 按配额比例多货源的尝试 (2020-2023)

#### Summary

MRP 部门曾提出希望 JIS 模块件能按百分比配额 (Quota) 进行订购。此需求面临巨大挑战：系统层面，IPT 和 AMS 难以支持，涉及多模块底层逻辑变动，开发复杂；运营层面，同厂区同工位双供应商或分天供货存在管理难题。最终 COC 明确不会开发此功能，系统无法支持。尽管 IT 拒绝后 MRP 未提交 BRD，但该议题后续仍被提及。

#### Storyline

- [2020-08-20] 收到郝学硕邮件，提出 JIS 按配额供货的需求。
- [2020-08-24] 刘锦华建议 BU 先明确需求，MRP 需与 JIS 规划协商可行性。
- [2020-09-02] 刘锦华反馈 COC 认为难度大，涉及底层逻辑。
- [2020-09-09] 刘锦华邮件答复 COC 不会制作此功能 (邮件主题: `AmS@BDA 差异T-Code讨论`)。
- [2020-09-09] 与 Ruestem 讨论后，建议放入 SFM 与 Christian Benz 讨论，邮件发送给 MRP 未获答复。
- [2023-08-22] 采购部门（黄晨男，吴立新，刘星）再次提及此事，转发了 2022 年 7 月相关讨论文件。

#### Reference

- 邮件:
  - [刘锦华邮件反馈](assets/答复__JIS_按百分比订货-20210324092101-3ofoa96.msg)
  - [同MRP L4的邮件, 含COC及project team的反馈](assets/Re2_答复_答复_答复_答复_答复_AmS_BDA_差异T-Code讨论_180_KB-20210324092228-e8hhpw5.msg)
  - [20230823084106 JIS Multi in 2022.msg](es://20230823084106%20JIS%20Multi%20in%202022.msg)
- 相关人员: 郝学硕, 刘锦华, Ruestem, Christian Benz, 黄晨男, 吴立新, 刘星

### 买卖件(Buy Back)功能强化需求 (2020-2021)

#### Summary

业务部门曾提出针对买卖件流程的优化需求，目标是实现供应商库存的虚拟化管理和国产/进口供应商排程的自动化生成。主要挑战在于虚拟库存管理的实地盘点复杂性以及处理库存差异对财务的影响。经过技术评估，确认系统功能无法按此需求实现。

#### Storyline

- [2020-12-xx] 收到业务部门关于 Buyback 零件订购支持的 BRD 及流程描述。
- [2021-01-xx] 进行技术可行性讨论。
- [2021-03-xx] 评估确认系统功能无法按此需求实现。

#### Reference

- 邮件: [20201224135628-8d1fvxy-答复_BRD_AM Supply支持buyback零件订购.eml](es://20201224135628-8d1fvxy-答复_BRD_AM%20Supply支持buyback零件订购.eml)

### 背景与定义

#### Summary

此部分概述了多货源管理的历史信息和关键概念。IMS 时期通过特定标识和按钮处理多货源选择。AmSupply 中通常使用 `Change Vendor` 添加多货源 SA。JIS 零件主数据需维护对应的 Receiver Group。顺义工厂的 JIS/JIT 多货源由 MRP 系统处理。JIS 多货源零件清单需主动从 RD-BVA 获取或关注多 SA 情况。

#### Storyline (2021年度重要事件摘录)

- [2021-01-13] 与 JIS 规划蒋文娟确认 GXX 蓝图细节。
- [2021-05-06] 与研发 Dennis 讨论需求，确认 24 个 MFA 多货源零件实施方案。
- [2021-07-01] 确认 JIS/JIT 多货源数据迁移规则：必须先维护 JIT 货源。
- [2021-11-03] 关键确认：DIALOG 中 Receiver Field (RG9) 可用。JIS 列表由 JIS Planning 定义，与 BVA 确认，RDC 录入。

#### Reference

- 关键概念图示:
  - IMS 多货源按钮: ![image.png](assets/image-20211130113913-c42yzv1.png)
  - IMS 供应商选择: ![image.png](assets/image-20211130113900-di45fjy.png)
  - MRP 系统处理截图: ![image](assets/image-20210415092724-s5g8hc2.jpg)
- 文档:
  - [AmS@BDA_Training_WP3_Multisourcing - wqukbvt.pptx](es://AmS@BDA_Training_WP3_Multisourcing%20-%20wqukbvt.pptx)
  - `DISCO - Change of Vendor - Multi-sourcing maintain - R&R及COC介绍文档`
- 邮件: `AW CGM assignment for JIS part migration.msg`
- 相关人员: [金玉](CRM/金玉.md), 蒋文娟, Dennis
- 相关链接: [JIS Receiver Group](AMS.BDA.Multi-sourcing/JIS%20Receiver%20Group.md) (`jis-receiver-group-zjwpc8`)

### 系统操作指南：多货源维护

#### Summary

本节介绍在 AmSupply 系统中维护多货源的具体操作步骤。主要包括通过 `Change Vendor` 功能添加新的 Item 行并维护 MRP Area（包括有效期、配额等），通过 `Change of Unloading Point` 功能调整 Item 行的卸货点 (ULP)，以及针对 JIS 零件的特殊配置（激活拆分并设置 Receiver Group）。

#### 通过供应商变更 (Change Vendor) 实现多货源

- 步骤:
    1. 使用 `Change Vendor` 复制 Item 行。
    2. 根据货源类型验证合同 (GSS/Globus)。
    3. 选择新旧 Item 行，维护 MRP Area (有效期, 配额)。
    4. 激活配置 (JIS 需指定 CGM, JIT 留空)。
- 参考图片:
  - ![image.jpg](assets/image-20210325115034-hmc0v9l.jpg)
  - ![image.jpg](assets/image-20210325131311-gcf89wc.jpg)
  - ![image.jpg](assets/image-20210325143959-qb4yajj.jpg)

#### 通过变更卸货点 (Change of Unloading Point) 调整 Item 行 ULP

- 流程: 创建 Disco 任务 -> 选择 Item 行更改 ULP -> 系统更新 MRP Area -> 设置 MRP Area 主数据 -> 激活。
- 相关配置: WebEDI 参数 `ALLOW_MULTIPLE_DOCKS` 已设为 `true`。
- 参考文档: `A013_通过Disco更改卸货点_change unloading point in disco_v01_20200211.docx`
- 参考邮件: `20211111 答复 V295 call off receive status .msg`

#### JIS 特殊配置：激活拆分并设置RG

- 在 JIS 零件的 MRP Area 主数据下，激活 `Splitting` 并设置 `RG - Receiver Group`。
- 参考图片: ![image.jpg](assets/image-20210324131111-46877vc.jpg)

### JIS 按车型拆分多货源实现方案 (Car Model Based)

#### Summary

利用 DIALOG 模块的 接收方组 (Receiver Group) 功能，根据不同车型的消耗来区分不同供应商的 JIS 供货。配置需在 DIALOG 中维护 RG，并遵循标准操作文档。零件清单需定期从 RD-BVA 同步。

#### Reference

- 配置图示: ![车型拆分配置](assets/JIS-Multisourcing-CarModel-Config.png)
- 维护指南: [JIS多货源车型拆分维护指南](AMS.BDA.Multi-sourcing/JIS-CarModel-Maintenance.md)

### 买卖件（进口/国产）多货源主数据维护规则

#### Summary

零件在进行首次 SA 创建 (Initial Setup) 时，必须严格遵守其采购类型 (BZA) 的设定。例如，BZA 为进口件 (`B7 L7`) 时，首次只能创建进口 SA。若需优先维护国产货源 SA，必须先请求 RD 部门将 BZA 调整为国产类型。

### JIS Receiver Group

#### Summary

JIS Receiver Group (RG) 是 DIALOG 系统中管理 JIS 货源的关键字段。RG1 标识供应商和位置/模块，不足时手动维护 RG9。RG 替代 IPT 中 BTID 作用，需在供应商变更前输入。RG9 与装焊 AMSR 字段共用，需注意维护冲突。

JIS Receiver Code Mapping:

| Receiver | Vendor   |
| :--------- | :--------- |
| JBE1     | ZF       |
| JZF1     | Benteler |
| JYF1     | Yanfeng  |
| JMA1     | Magna    |
| JBR1     | Brose    |
| JLE1     | Lear     |
| JLR1     |          |

#### Storyline

- [2023-05-10] 计划与 RD 对齐 RG 维护流程，讨论 RG9/AMSR 共用及新流程设想。
- [2023-05-15] 与物流团队讨论 "J + 供应商代码后三位" Receiver 格式可能性。
- [2023-05-29] 与 RD 团队介绍 JIS Receiver，聚焦房旭负责的 loop。
- [2023-06-02] 咨询房旭，下周将组织会议讨论 loop 事宜。

#### Reference

- 系统维护截图:
  - ![系统维护界面2](assets/image-20210331151448-k92c5xk.jpg)
  - ![系统维护界面3](assets/image-20210331151633-ab9cx8f.jpg)
  - ![RG示例](assets/image-20240531141926-v9ecme1.png)
- 相关人员:
  - RD: [zhao-xinyu-z1f5gas](CRM/赵新宇.md), [姚琼](CRM/姚琼.md), 苑英楠
  - LOG: [房旭](CRM/房旭.md), [郑思闽](CRM/郑思闽.md), [蒋文娟](CRM/蒋文娟.md)

### JIT Multi-sourcing by Quota Order

#### Summary

曾讨论过 JIT 多货源配额订单 (Quota Order) 功能的上线优先级，考虑过延后。但根据 V213 装焊使用案例等反馈，确认该功能是业务必需的，并已反馈给 COC。

#### Storyline

- [2021-01-29] 内部讨论或系统截图记录了关于 Quota Order 的情况。
  - ![Quota Order 相关截图1](assets/20210201155835-eb1wgro-image.jpg)
  - ![Quota Order 相关截图2](assets/20210201155823-a4z1i81-image.jpg)

#### Reference

- 无显式参考资料，除 Storyline 中的截图外。

### JIT Multi-sourcing within a Single Car Model (Gap)

#### Summary

探讨了在非 Multi-sourcing 场景下应优先使用 Running Change，否则需大量手工操作。针对 Multi-sourcing，特别是同一车型内使用相同零件但来自不同供应商的情况（AMS 无法直接支持），提出了解决方案。
核心问题是 AMS 无法像 IPT 那样区分单一车型内（如 V213 汽油 vs. 混动）同一零件号 (A2136111301, A2136111401) 的不同供应商需求。
最终解决方案是为混动 V213 车型使用的零件添加 ES1 代码 (如 A 213 611 13 01 5850)，并修改对应的 Baukasten BOM 结构，使 DIALOG 系统能区分需求。此方案已获 RD 同意并成功通过 KSV 测试。

涉及零件 (截至 2022-04-01):

- A 213 611 13 01 (已解决)
- A 213 611 14 01 (已解决)
- A 253 630 00 00
- A 253 637 47 00
- A 253 680 17 25
- A 000 682 00 00

#### Storyline

- [2020-12-07] Gap 在 PM Meeting 上由 WP2 展示。
- [2020-12-16] Florian Schneider 介绍替代方案：RD 设置新的 ES1 零件号。
- [2020-12-22] 张同指出 BBAC RD 无法直接添加 ES1 code，需 BTV 释放 KEM。
- [2020-12-23] 张辉向 Gao Po 解释多货源原因。
- [2021-01-05] Gao Po 回复可考虑增加 deviation L8。
- [2021-01-05] Florian Schneider 会议讨论 ES1 和改 JIS 方案。
- [2021-01-06] Daimler RD 答复可以进行 ES 码操作。
- [2021-01-13] Globus 无法处理 ES1，需等 ProQ 更新。
- [2021-04-30] Oliver Rohatsch 表示 ES1 是 ProQ 标准功能。
- [2022-02-18] 准备 MRA SA Validation List 时发现问题仍存。
- [2022-03-30] 宋庆庆反馈两个零件 Header 号不同。
- [2022-04-06] 准备会议 PPT。
- [2022-04-15] 与胡青平 (RD) 开会确认不能添加零件号。
- [2022-04-19] WP2, WP6, 胡青平会议，检查 ES1 和 manual control 方案。
- [2022-04-26] 与 RD 第二次会议，讨论 ES1 Key 和 Call off ZB from Gestamp 方案。
- [2022-05-10] 与胡青平、宋庆庆会议，否定方案 2，关注 ES1 方案。
- [2022-05-13] Dokhaus 会议，赵新宇介绍 ES1 不在 BOM 中。
- [2022-05-16] 与 Florian, Dennis, Stefanie 会议确认可通过修改 Baukasten BOM 控制。
- [2022-05-27] KSV 测试成功，确认 Hybrid 零件号变更。
- [2022-06-23] 合同完成。
- [2022-08-16] 与李建群沟通切换细节。
- [2022-10-03] 在 DISCO 中激活新 ES1 零件号合同。

#### Reference

- 汇总 PPT: [JIT in one model multisourcing - A2136111301 A2136111401 - 202204061350.pptx](es://JIT%20in%20one%20model%20multisourcing%20-%20A2136111301%20A2136111401%20-%20202204061350.pptx)
- Fit-Gap 分析: [AmS@BDA_Fit-Gap_Evaluation_WP3_JIT-Multisourcing_20201122.pptx](es://AmS@BDA_Fit-Gap_Evaluation_WP3_JIT-Multisourcing_20201122.pptx)
- 邮件记录 (部分):
  - [AWAWDIALOG104uniquematerialtoGLOBUS.eml](assets/20210113174259-ycfmkhi-AW%20AW%20DIALOG104%20unique%20material%20to%20GLOBUS.eml)
  - [20220307 回复 AW DIALOG104 unique material to GLOBUS.msg](es://20220307%20回复%20AW%20DIALOG104%20unique%20material%20to%20GLOBUS.msg)
  - [20220516 RE ES1 Key fuer JITJIT Splitting Teil.msg](es://20220516%20RE%20ES1%20Key%20fuer%20JITJIT%20Splitting%20Teil.msg)
- 相关人员: [陈亚娟](CRM/陈亚娟.md), [李建群](CRM/李建群.md), [杨井卫](CRM/杨井卫.md), [张桐](CRM/张同.md), [hu-qingping-2siobu](CRM/胡青平.md), [宋庆庆](CRM/宋庆庆.md), [yuan-gang-wetk5](CRM/袁刚.md), [Florian Schneider](CRM/Florian%20Schneider.md), [Stefanie Straub](CRM/Stefanie%20Straub%20.md), [Dennis Kussmaul](CRM/Dennis%20Kussmaul.md), [zhao-kang-z1arfwk](CRM/赵康.md), [ju-luning-kajie-18hivi](CRM/琚鲁宁.凯捷.md), [Oliver Rohatsch](CRM/Oliver%20Rohatsch.md), [zhang-hui-z2qbamj](CRM/张辉.md), [Gao Po](CRM/Gao%20Po.md), [Tan Dong](CRM/Tan%20Dong.md)
- 其他: [Quire 任务链接](https://quire.io/w/AmS.BDA/19/JIT_Multi-Sourcing_in_AmS?view=tree), [EDI 测试机信息](Projects/EDI/EDI测试机.md), [AmS@BDA_Organizational Structure_V0.99f.pptx](es://AmS@BDA_Organizational%20Structure_V0.99f.pptx)
- 图示:
  - ![Gap 展示截图](assets/20210105133302-cu7zdyd-20201204011916-otpgb04-image.jpg)
  - ![Gao Po 回复截图](assets/20210105105255-xlyoroi-image.jpg)
  - ![Daimler RD 回复截图](assets/20210106104317-zxy1gdf-image.jpg)
  - ![IPT 工作原理](assets/image-20210324094627-ak5g5vc.jpg)
  - ![解决方案示例1](assets/image-20220406134349-zr2u7da.png)
  - ![解决方案示例2](assets/image-20220406135747-3biytrg.png)
  - ![解决方案示例3](assets/image-20220406135844-6jsrgd9.png)
  - ![解决方案示例4](assets/image-20220406140655-av6xpg7.png)
  - ![KSV 测试成功截图](assets/image-20220527150952-v1mksfj.png)
  - ![合同审批状态截图](assets/image-20220616170035-qagb2az.png)
  - ![重新贴签讨论截图](assets/image-20220707153448-esqs0o3.png)
  - ![与李建群沟通截图](assets/image-20220815113828-m7tjgxe.png)
  - ![KSV 系统检查截图1](assets/image-20220907152929-kkhgy5d.png)
  - ![KSV 系统检查截图2](assets/image-20220907153129-vgimgle.png)
  - ![KSV 系统检查截图3](assets/image-20220907153341-wxe2eiz.png)
  - ![MRP 运行结果截图](assets/image-20221003154307-kw01kgi.png)

### V214/V206 Multi-sourcing Issues

#### Summary

处理 V214 (国产) 和 V206 (进口) 车型 JIT/JIT 零件多货源问题。起因是 MRP 询问如何为同一零件区分订单给不同供应商，而 AMS 系统无法直接支持。关键在于 RD 何红确认这些零件在 BBAC 生产线可混用，无需强制区分订购。因此决定等待 PS 部门确定国产化切换时间点后统一供应商。议题于 2022 年 11 月 22 日关闭。

#### Storyline

- [2022-08-26] MRP 咨询 V206/V214 JIT/JIT 订单拆分问题。
- [2022-08-31] 整理涉及的零件清单。
- [2022-09-01] Florian 答复 AMS 无法实现此种区分。
- [2022-09-06] 在 PM Call 上展示此问题，向 RD 提出疑问。
- [2022-09-14] 与 MMT, Christian Reuter 会议，决定要求 SOP 前统一 Vendor。
- [2022-09-22] V214 项目组专项会议，何红 (RD) 确认零件可混用，无需分开订购。
- [2022-11-22] 议题关闭。

#### Reference

- 核心 PPT: [JIT in one line multisourcing - V206&214- 202209011715.pptx](es://JIT%20in%20one%20line%20multisourcing%20-%20V206&214-%20202209011715.pptx)
- 邮件/文件包 (部分):
  - [20220826 RE A2069004016需求问题_20220826_065511.zip](es://20220826%20RE%20A2069004016需求问题_20220826_065511.zip)
  - [20220922 W104 V214 JITJIT Multi Sourcing issue new demand meeting minutes 20220922_20220922_034048.zip](es://20220922%20W104%20V214%20JITJIT%20Multi%20Sourcing%20issue%20new%20demand%20meeting%20minutes%2020220922_20220922_034048.zip)
  - [20221122 答复 A2069004016 Localization_20221122_050206.zip](es://20221122%20答复%20A2069004016%20Localization_20221122_050206.zip)
- 相关人员: [黄静](CRM/黄静.md), [姚琼](CRM/姚琼.md), [Florian Schneider](CRM/Florian%20Schneider.md), [Christian Reuter](CRM/Christian%20Reuter.md), [Mehmet Tas](CRM/Mehmet%20Tas.md), [何红](CRM/何红.md), [董静](CRM/董静.md)
- 图示:
  - ![内部沟通记录截图](assets/image-20220909143247-6xizunx.png)
  - ![会议相关截图](assets/image-20220914162730-vxd43p8.png)
  - ![与何红沟通截图](assets/image-20220922154033-2eo9lw3.png)

### V213/V214 Multi-sourcing (Reference Only)

#### Reference

- 相关邮件: [答复 A2058301201---Duel source.msg](es://答复%20A2058301201---Duel%20source.msg) (具体内容需查看邮件本身)

### 《多货源系统支持总结》

#### Summary

本节总结了 IT 系统支持多货源的基础（主数据维护不同 RG 和 MRP Area），列出了已支持的 7 种多货源形式及其示例，并对 BJIS/JIS、相同零件号 JIT/JIT、不同零件号 JIT/JIT（互选件）三种形式的系统现状和建议方案进行了阐述。最后建议针对高价值零件，优先选择已支持的 7 种形式启动多货源。

#### 一、IT系统支持多货源的基础

- 主数据维护不同的 Receiver Group 和不同的 MRP Area。
- ![基础图示](assets/20250408_153905_d50b18e346cd1f9113a87288276ab621-image.png)

#### 二、IT系统已支持的7种多货源形式

- 系统中均存在正在运行的实例。
- 表格: (见下)

| 示例 | 场景                                                     | 多货源方式                        | 零件号                                                        | 供应商                                                                                    |
| ------ | ---------------------------------------------------------- | ----------------------------------- | --------------------------------------------------------------- | ------------------------------------------------------------------------------------------- |
| 1    | 相同零件号，相同生产线，相同车型，不同装配位置           | JIS(电池)& JIS(仪表)(MFAH247)     | A 177 906 00 02(Wiring Harness)                               | JIS1: Langfang LEONI Wiring System JIS2: Beijing BAIC Yanfeng Automotive Par              |
| 2    | 相同零件号，相同生产线，相同车型，不同装配位置           | JIS& JIT(MRA1 V214)               | N 000000 008345(Screw)                                        | JIS: Beijing BAIC Yanfeng Automotive Par JIT:进口(Mercedes-Benz AG)                       |
| 3    | 相同零件号，相同生产线，不同车型，不同装配位置           | JIS(V206)& JIS(H243)(MFA)         | N 000000 001114(HXRD SCREW)                                   | JIS1: ZF Chassis System(Beijing) Co., Lt JIS2: Benteler Automotive(Tianjin) Co.,          |
| 4    | 相同零件号，相同生产线，不同车型，不同装配位置           | JIS(V206)& JIT(H247)(MFA)         | A 005 990 47 50(COMBINATION NUT)                              | JIS:ZF Chassis System(Beijing) Co., Lt JIT:进口(Mercedes-Benz AG)                         |
| 5    | 相同零件号，不同生产线，相同/不同车型，相同/不同装配位置 | JIS(MFA X247)& JIS(MRA2 V214)     | A 000 352 00 76(CAM DISC)                                     | JIS1 MFA: Benteler Automotive(Tianjin) Co., JIS2 MRA2: ZF Chassis System(Beijing) Co., Lt |
| 6    | 相同零件号，不同生产线，相同/不同车型，相同/不同装配位置 | JIS(MRA2 V254)& JIT(MFA V206)     | A 000 900 48 25(CU COMPL INTERIOR - LIGHT PREMIUM - EQUIPMEN) | JIS: Beijing BAIC Yanfeng Automotive Par JIT:进口(Mercedes-Benz AG)                       |
| 7    | 相同零件号，不同生产线，相同/不同车型，相同/不同装配位置 | JIT(进口)&JIT(国产)切换时短期存在 | N 000000 003175(NUT)                                          | JIT:内德史罗夫紧固件(昆山) 有限公司 JIT:进口(Mercedes-Benz AG)                            |

#### 三、不同多货源形式的具体情况

- （一）BJIS&JIS多货源形式
  - 系统方案现状: 基于 Quota 的方案已完成需求澄清与技术评估，成本已反馈，待业务决定实施。
  - 建议的手工方案: 参考轮胎 JIS 方案，由 Tier1 控制 Tier2 货源比例。
- （二）相同零件号的JIT&JIT多货源形式 (参考下面的48V电池案例, 手工控制风险很大)
  - 系统方案现状: AmS 通过 Quota 支持待验证，暂无业务应用场景。
  - 建议的手工方案: 人工控制订购比例和库存平衡。
- （三）不同零件号的JIT&JIT多货源形式（互选件）

  ##### 案例分析：48V 电池供应切换 (2024)

  ###### Summary

    > ![image](assets/image-20240914144911-bm1w66a.png)
    >
    > 原本国产48V电池 (A 000 982 59 31 / A 000 982 60 31) 在 V214 车型上使用, 但因合同原因, 2024年国产件只能供应到10月底。研发(RD)不同意提供 Production Equipment Modification (PEM) 以切换供应商, 只同意在 V214 上永久开放国产/进口件的互选。然而，互选会导致需求预测 (demand) 加倍，带来计划和库存风险。经过管理层会议，RD最终同意提供PEM。

    | Material        | Source | Model   |
    | --------------- | ------ | ------- |
    | A 000 982 59 31 | 国产   | MY 23/2 |
    | A 000 982 78 24 | 进口   | MY 23/2 |
    | A 000 982 60 31 | 国产   | MY 24/1 |
    | A 000 982 57 30 | 进口   | MY 24/1 |

    文件夹位置:[48V battery multisource Soluton](es://48V%20battery%20multisource%20Soluton)

    <iframe style="border: 1px solid rgba(0, 0, 0, 0.1); width: 1013px; height: 553px;" sandbox="allow-scripts allow-popups allow-forms allow-modals allow-same-origin" width="800" height="450" src="https://boardmix.cn/app/share/CAE.CLClow0gASoQ1YgMI_YDx71F4z9WmJ1FNTAGQAE/UQ2laM?elementNodeGuid=1:188" data-src="https://boardmix.cn/app/share/CAE.CLClow0gASoQ1YgMI_YDx71F4z9WmJ1FNTAGQAE/UQ2laM?elementNodeGuid=1:188" allowfullscreen=""></iframe>

  ###### Storyline

    [2024-09-09] 接到E3/E2任务, 对48V电池供应问题进行分析。

    [2024-09-11] 同MRP、管理科开会, 反馈分析报告。
    ![image](assets/image-20240914145248-n56qf0i.png)

    [2024-09-14] 目前E2联系Fabian进行解释, 暂未联系BBAC。

    [2024-09-20] 接到E2任务, 制作PPT阐述为什么MB plants能用互选而BBAC不行。参考Fabian的输入: [48V Optional Use Analysis_20240920.pptx](es://48V%20Optional%20Use%20Analysis_20240920.pptx), [48V Battery Optional Use Analysis.eml](es://48V%20Battery%20Optional%20Use%20Analysis.eml)

    > Fabian反馈: This method was used years ago, but has been stopped because risk of line-stop and additional HC.
    > Currently, no MB plants operate with this 'multi-source' (alternative) approach.
    > If both sources are available, they will only active one for ordering (quota set at 100% - 0%).

    [2024-09-23] 得到E2要求:
    1. 提供质量部门反馈 ![image](assets/image-20240923145106-dwq0h0x.png)![image](assets/image-20240923155815-21s33d7.png)
    2. 用图画解释Quota分配机制。

    [2024-09-24] 关于零件追踪性的反馈: [20240924_回复_ 48V电池混用咨询.eml](es://20240924_回复_%2048V电池混用咨询.eml)

    [2024-09-30] 经过管理层会议, RD提供了PEM以解决供应切换问题。 [20240930 回复_ 48V battery _ Volume reduction local PN in 2024.eml](es://20240930%20回复_%2048V%20battery%20_%20Volume%20reduction%20local%20PN%20in%202024.eml) [20241009 转发_ 48V battery _ Volume reduction local PN in 2024.eml](es://20241009%20转发_%2048V%20battery%20_%20Volume%20reduction%20local%20PN%20in%202024.eml)

  ###### Reference

    ####### Participants

    [潘超](CRM/潘超.md) [于丹](CRM/于丹.md)

    [杨一鸣](CRM/杨一鸣-LOG.md)

    RD: [张宝钢](CRM/张宝钢.md)，[刘紫](CRM/刘紫.md)
    ![image](assets/image-20240923160006-jfc13h2.png)

    [Steffen Hunzinger](CRM/Steffen%20Hunzinger%20-%20E2.md)

    [fabian_marcel.kamilli](CRM/fabian_marcel.kamilli.md)

    ####### Related Documents/Links

  - Folder: [48V battery multisource Soluton](es://48V%20battery%20multisource%20Soluton)
  - Analysis PPT: [48V Optional Use Analysis_20240920.pptx](es://48V%20Optional%20Use%20Analysis_20240920.pptx)
  - Emails:
    - [48V Battery Optional Use Analysis.eml](es://48V%20Battery%20Optional%20Use%20Analysis.eml)
    - [20240924_回复_ 48V电池混用咨询.eml](es://20240924_回复_%2048V电池混用咨询.eml)
    - [20240930 回复_ 48V battery _ Volume reduction local PN in 2024.eml](es://20240930%20回复_%2048V%20battery%20_%20Volume%20reduction%20local%20PN%20in%202024.eml)
    - [20241009 转发_ 48V battery _ Volume reduction local PN in 2024.eml](es://20241009%20转发_%2048V%20battery%20_%20Volume%20reduction%20local%20PN%20in%202024.eml)

  - 系统方案现状: 发动机工厂有 IPT 方案；其他动力总成工厂基于 Baukasten Generator 的方案待验证；整车厂暂不支持。
  - ![互选件图示](assets/20250408_153952_9369cabef87fe.png)

#### 四、建议方案

- 针对高价值零件，选择 IT 系统已支持的 7 种多货源形式立即启动多货源供货。
- ![建议方案图示](assets/20250408_154000_1e043690c19e5.png)

## Reference (Overall)

此部分汇总文档中涉及的关键参考资料、联系人、系统和术语。

- 核心文档/PPT:
  - [AmS@BDA_Training_WP3_Multisourcing - wqukbvt.pptx](es://AmS@BDA_Training_WP3_Multisourcing%20-%20wqukbvt.pptx)
  - [AmS@BDA_Fit-Gap_Evaluation_WP3_JIT-Multisourcing_20201122.pptx](es://AmS@BDA_Fit-Gap_Evaluation_WP3_JIT-Multisourcing_20201122.pptx)
  - [JIT in one model multisourcing - A2136111301 A2136111401 - 202204061350.pptx](es://JIT%20in%20one%20model%20multisourcing%20-%20A2136111301%20A2136111401%20-%20202204061350.pptx)
  - [JIT in one line multisourcing - V206&214- 202209011715.pptx](es://JIT%20in%20one%20line%20multisourcing%20-%20V206&214-%20202209011715.pptx)
  - [AmS@BDA_Organizational Structure_V0.99f.pptx](es://AmS@BDA_Organizational%20Structure_V0.99f.pptx)
  - `DISCO - Change of Vendor - Multi-sourcing maintain - R&R及COC介绍文档`
  - `A013_通过Disco更改卸货点_change unloading point in disco_v01_20200211.docx`
- 邮件/文件包 (部分示例):
  - [AWAWDIALOG104uniquematerialtoGLOBUS.eml](assets/20210113174259-ycfmkhi-AW%20AW%20DIALOG104%20unique%20material%20to%20GLOBUS.eml)
  - [20220826 RE A2069004016需求问题_20220826_065511.zip](es://20220826%20RE%20A2069004016需求问题_20220826_065511.zip)
  - [答复 A2058301201---Duel source.msg](es://答复%20A2058301201---Duel%20source.msg)
  - [刘锦华邮件反馈](assets/答复__JIS_按百分比订货-20210324092101-3ofoa96.msg)
  - [同MRP L4的邮件, 含COC及project team的反馈](assets/Re2_答复_答复_答复_答复_答复_AmS_BDA_差异T-Code讨论_180_KB-20210324092228-e8hhpw5.msg)
  - [20201224135628-8d1fvxy-答复_BRD_AM Supply支持buyback零件订购.eml](es://20201224135628-8d1fvxy-答复_BRD_AM%20Supply支持buyback零件订购.eml)
  - (更多邮件和 ZIP 文件请参见各子章节的 Reference 部分)
- 关键系统/工具: DIALOG, AMS, IPT, ProQ, KSV, Globus, WebEDI, Quire
- 关键术语: Multi-sourcing, JIT, JIS, Receiver Group (RG1, RG9), Quota Order, ES1 Code, Linefeed, BTID, MRP Area, Running Change, Baukasten BOM, AMSR, KEM, DISCO, SA Validation, Dokhaus, CGM (Consumption Group), ULP (Unloading Point), BZA (采购类型), Buy Back.
- 涉及部门/角色 (部分): RD (研发), LOG (物流), Purchasing (采购), P&S (采购), MRP, JIS Planning, PM (项目管理), WP (工作包), COC, PS (生产策略/规划), IT, Controller, RDC (文档中心), BVA.
- CRM 联系人: (请参见各子章节的 Reference 部分)
- 相关文档:
  - [JIS Receiver Group](AMS.BDA.Multi-sourcing/JIS%20Receiver%20Group.md)
  - [JIS多货源车型拆分维护指南](AMS.BDA.Multi-sourcing/JIS-CarModel-Maintenance.md)
  - [EDI 测试机信息](Projects/EDI/EDI测试机.md)

