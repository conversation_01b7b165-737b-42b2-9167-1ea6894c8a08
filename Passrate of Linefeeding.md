﻿---
title: "Passrate of Linefeeding"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# Passrate of Linefeeding

> 计算TO完成时间与生成时间的差值，根据零件类别和配送路径KPI标准来判断每个TO用时是否达标，统计合格率

> ### Qlik Sense
>
> [服务器地址变更](.md#**************-sm7m03t)
>
> [https://qliksense-dev.app.corpintra.net/emea/hub](https://qliksense-dev.app.corpintra.net/emea/hub)
>
> [https://qliksense.app.corpintra.net/emea/hub](https://qliksense.app.corpintra.net/emea/hub)

后台主数据excel版本

# 逻辑

使用`Destination SU`​&`Routing ID`​作为合并TO的依据。

首先判断用时>24H则给红灯。

在时间<24H的情况下，对比用时(Elapsed Time)和[Z_STDTM1](EDW%20后台主数据.md#**************-jhpxz7w)中的标准时间。

![image](assets/image-**************-ta6sqmr.png)[zip](es://********%20EDW%20IPTAmSupply%20switch%20-%20Passrate%20of%20Linefeeding_********_041028.zip)

![image](assets/image-**************-279jt5c.png)​

![image](assets/image-**************-ht6j9ro.png)​​

![20221027135501_RE ReRE EDW IPTAmSupply switch - Passrate of Linef](assets/20221027135501_RE%20ReRE%20EDW%20IPTAmSupply%20switch%20-%20Passrate%20of%20Linef-**************-36lfxx3.jpg)

​

# 数据验证 ********

[EDW 数据匹配及计算结果说明 - logic - ********.pptx](es://EDW%20数据匹配及计算结果说明%20-%20logic%20-%**********.pptx)

‍

‍

