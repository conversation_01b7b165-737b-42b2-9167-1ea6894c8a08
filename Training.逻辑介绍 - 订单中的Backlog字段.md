﻿---
title: "Training.逻辑介绍 - 订单中的Backlog字段"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 逻辑介绍 - 订单中的Backlog字段

‍

# 💻 逻辑描述

订单(Schedule Line)的日期在过去而没有收货的数量, 将被记入 Backlog

# 🧾 简单举例

![Untitled](assets/Untitled-20220726090758-875gl00.png)

1. 此例 Call Off 日期为 7 月 21 日
2. 系统提示 Call Off 中存在 Backlog
3. 在 Schedule Line 详情中标注了 Backlog 行. 请注意该行数值为所有 Backlog 的汇总, 并非是只有 7 月 20 日存在 Backlog.

# 📈 进阶讲解

请小心在某些系统命令中, Backlog 作为单独的一项进行显示, 并没有按照日期进行标注.

![Untitled](assets/Untitled%201-20220726090758-eewt8on.png)

# 📰 文档信息

> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>
> 初次发布时间：2022-03-15
>
> 最后更新时间：2022-07-22
>
> 版本: V1.1

‍

