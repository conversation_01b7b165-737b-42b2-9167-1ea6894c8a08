﻿---
title: "LSO.V206 MY251 ET试装- 白车身A2066002802反冲问题"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "问题解决"

category: "Issue"
project: "系统优化"
---
# V206 MY251 ET 试装- 白车身 A2066002802 反冲问题(AmSupply LSO)

## Summary

**核心问题**: V206 MY251 ET 试装中白车身 A2066002802 在 AmSupply LSO 逻辑下错误反冲到序列化成本的问题。

**当前状态**: COC 已制定分阶段解决方案，2025 年 2 月 3 日起将实施自动化方案，根据 BVA 值区分处理逻辑。(已完成)

**关键结论**:

- 原因：LSO 逻辑采用量产 BIW 而非一次性采购，导致反冲逻辑异常
- 解决方案：分四个阶段逐步调整，最终实现 BVA=LOG 发送给 cbFC，BVA=DEV 不发送
- 影响范围：所有 LSO 试装（含 Bphase 和 ET）的白车身反冲处理

## StoryLine

📅2024-10-21: 李明奎组织会议说明问题（V206M25/1 ET）。确认不应该反冲到序列化成本里，LSO 反冲逻辑不考虑白车身逻辑。本次年度型使用 LSO 逻辑采用量产 BIW 而非一次性采购导致问题。提出长期方案：BIW 都不自动冲，改为手动反冲。

📅2024-11-19: 李明奎同 COC 讨论后安排会议解释后续方案。COC 否决之前长期意见，制定新方案：BIW 都参与反冲，但 RD 的 BIW 不发送给 CBFC。制定四阶段实施计划：

1. 2024.01.01-2024.11.18：排查历史 LSO 订单白车身反冲情况
2. 2024.11.19-2024.12.08：反冲到序列化成本，都发给 cbFC（GIPR）
3. 2024.12.09-2025.02.02：反冲到 IO，都发给 cbFC（GIAO），BVA=DEV 需退反冲
4. 2025.02.03 起：反冲到 IO，BVA=LOG 发给 cbFC，BVA=DEV 不发

## Reference

**核心人员**:

- 李明奎 (项目负责人)
- 王宇 (技术支持)
- Bobby, 陈朱颖, 姚琼, 王东, 张同, 柳清溪 (参与人员)

**相关文档**:

- [202410211631 回复 V206 MY25_1 ET 试装白车身 A2066002802 反冲问题.eml](<es://202410211631_回复_%20V206%20MY25_1%20ET试装-%20白车身A2066002802反冲问题(AmSupply%20LSO).eml>)

**技术要点**:

- BIW：Body In White，白车身
- AmSupply 系统反冲机制
- BVA 值区分：LOG vs DEV
- cbFC 系统集成（GIPR/GIAO）

