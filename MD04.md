﻿---
title: "MD04"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# MD04

[AmS.BDA.Gaps.Missing DepReq in MD04](Projects/AmS@BDA/Gaps%20&%20Issues/更改需求显示逻辑回到IPT模式/AmS.BDA.Gaps.Missing%20DepReq%20in%20MD04.md)

[AmS.MD04 OrdRes 汇总显示](MD04/AmS.MD04%20OrdRes%20汇总显示.md)

[逻辑介绍 - MD04需求拆分显示](Projects/AmS@BDA/AMS.BDA.Training/AmS%20系统需求显示及计算逻辑/逻辑介绍%20-%20MD04需求拆分显示.md)

[逻辑介绍 - MD04 - End Replenishment Lead Time](Projects/AmS@BDA/AMS.BDA.Training/逻辑介绍%20-%20MD04%20-%20End%20Replenishment%20Lead%20Time.md)

‍

# Selection Rule

![image.png](assets/image-20211112103309-axgk8bt.png)

📅20211008  
需要张嘉描述, 然后发给 [Roland](CRM/Roland%20Herold.md)

预计 11 月更新

📅20211103 系统已传入, close topic

📅20221017 AMS@MRA上线后，梳理了系统中所有的rules，并邮件通知MRP是否需要修改，提醒其之后修改就得走大流程了。[20221026 MD04 Selection Rule_20221026_043444.zip](es://20221026%20MD04%20Selection%20Rule_20221026_043444.zip)

📅20221110 [禄魁](CRM/禄魁.md)提出需求要对104的零件增加rules，反馈其需要BRD后一直未答复。[20221201 RE MD04 selection rule.msg](es://20221201%20RE%20MD04%20selection%20rule.msg)

# Change Navigation in MD04

![image.jpg](assets/image-20210708135906-km3iqpd.jpg)

![image.jpg](assets/image-20210708135917-vzrzb65.jpg)

