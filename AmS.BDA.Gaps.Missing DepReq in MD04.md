﻿---
title: "AmS.BDA.Gaps.Missing DepReq in MD04"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# AmS.BDA.Gaps.Missing DepReq in MD04

# ⭐️ Key

# 📆 Storyline

📅20210610 培训时候发现AMS中的MD04命令里没有DepReq类别需求, 无法详细显示需求的车型信息.

![image.jpg](assets/image-20210617133839-043x6mw.jpg)

根据[Christoph](CRM/Christoph%20Muench.md)答复, AMS中没有相应的信息.

[AW__10_Jun_Meeting_Minutes__RE__AmS_BDA_WP3_Key_User_Training_General_2.msg](assets/AW__10_Jun_Meeting_Minutes__RE__AmS_BDA_WP3_Key_User_Training_General_2-20210617133924-8rf2fox.msg)

📅20211105 上 PM 会, 如果恢复 IPT 近似逻辑, 可以使用 query 替代.

PM 提出咨询其他工厂如何操作.

📅20211111 Sindelfingen [<PERSON>](CRM/Julia%20Siomchanka.md) 咨询付近近, 具体描述其需要车号干什么用.

补录：📅20230717 最后开发的Query应该是[ZIBVBMAT](Projects/AmS@BDA/AMS.BDA.Training/操作文档%20-%20查询零件所用车型、车号、用量%20-%20ZPCVBMAT.md)

‍

