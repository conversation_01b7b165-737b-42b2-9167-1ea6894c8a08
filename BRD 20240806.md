﻿---
title: "BRD 20240806"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# BRD 20240806

⭐️ Key

/  
/

## 📆 Storyline

📅20240806 first meeting by 李瑞瑞 to introduce the BRD [BRD Preview-Requirement summary_ AMSupply系统增强_202408_李瑞瑞.eml](es://BRD%20Preview-Requirement%20summary_%20AMSupply系统增强_202408_李瑞瑞.eml)

> 1. LSO 发送订单时, 增加一个commnet列
> 2. 需求打散在AF区域, 需要手动增加SPT
> 3. Disco UP Change操作的时候, 会自动删除AF区域

![PixPin_2024-12-04_09-55-53](assets/PixPin_2024-12-04_09-55-53-20241204101324-p6rnb4l.png)

📅20241204 收到Bremen的Demand 1000003089, 他们遇到了自动删除JIS Area的问题, 想修改.

> \*\*管理摘要\*\*目前，在特定JIS拆分问题的情况下，当在Dispo-Cockpit中更改卸货点时，JIS-MRP区域会自动删除。 供应链经理不会收到通知，也无法纠正这种情况。 这将取消供应商的JIS预览，并可能导致零件缺失和生产中断。\*\*实施理由\*\*由于存在零件缺失和生产中断的风险，应尽快实施此需求。\*\*潜在描述\*\*避免零件缺失和生产中断。\*\*需求类别\*\*有效MRP区域的自动删除可能导致零件缺失和生产中断，因为供应商的JIS预览被取消。 未来必须防止这种情况发生。\*\*目标状态\*\*在自动分配的情况下，不得删除超额提货区域，而必须显示错误消息。下一步，必须执行手动分配，届时将显示要删除的超额提货区域。 必须能够编辑此内容。 这个过程也必须能够大规模进行。

‍

## 🔗 Reference

### Participants

李明奎, 李淳

初慧怡, 李瑞瑞, 王晨

‍

