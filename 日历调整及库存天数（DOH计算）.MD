﻿---
title: "日历调整及库存天数（DOH计算）"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---

当涉及工作日历（Working Calendar）调整时，需要注意MD04中DOH计算逻辑

我提醒一下MRP的同事,以前处理类似事情时需要MRP的同事锁定订单.

否则

| 当前      | 工作日 | DOH | 订单 |
| --------- | ------ | --- | ---- |
| 2025/4/15 | Y      | 3   | Y    |
| 2025/4/16 | Y      | 3   | Y    |
| 2025/4/17 | Y      | 3   | Y    |
| 2025/4/18 | Y      | 3   | Y    |
| 2025/4/19 | N      |     |      |
| 2025/4/20 | N      |     |      |
| …        | N      |     |      |
| 2025/5/6  | N      |     |      |
| 2025/5/7  | N      |     |      |
| 2025/5/8  | N      |     |      |
| 2025/5/9  | N      |     |      |
| 2025/5/10 | Y      | 3   | Y    |

假设Safety Time设置为3的情况下, 因为4月19日为工作日且没有需求, 因此系统认为16日开始不需要订货也能满足Safety Time要求.

且因为系统认为7号开始为工作日, 为了满足10号的需求, 会从7号开始生成订单.

| 调整后    | 工作日 | DOH | 订单 |
| --------- | ------ | --- | ---- |
| 2025/4/15 | Y      | 3   | Y    |
| 2025/4/16 | Y      | 3   |      |
| 2025/4/17 | Y      | 3   |      |
| 2025/4/18 | Y      | 3   |      |
| 2025/4/19 | Y      |     |      |
| 2025/4/20 | Y      |     |      |
| …        | Y      |     |      |
| 2025/5/6  | Y      |     |      |
| 2025/5/7  | Y      | 3   | Y    |
| 2025/5/8  | Y      | 3   | Y    |
| 2025/5/9  | Y      | 3   | Y    |
| 2025/5/10 | Y      | 3   | Y    |

