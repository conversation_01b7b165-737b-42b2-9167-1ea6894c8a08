﻿---
title: "Training.逻辑介绍 - MD04 - End Replenishment Lead Time"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 逻辑介绍 - MD04 - End Replenishment Lead Time

# 现象及原因描述

MD04 中，这条线用于显示一个距离今日特定日期间隔的日期，

![](assets/clip_image002-20211102181809-fqveeg7.jpg)

一般这条线用于提示冻结区的范围，但需要先进行主数据维护才会显示出来。

主数据维护完成后，通过该开关可以控制这条线是否显示

![Machine generated alternative text:](assets/clip_image004-20211102181809-h1zr10s.jpg)

# 简单举例

## 原始状态

104 下面 on/off 是灰色

![image.png](assets/image-20211102181921-vxzwqrk.png)

## 维护 MM 主数据

MRP2: Planned Delivery time ->5 天 -> 保存

![image.png](assets/image-20211102181930-bnf6bcm.png)

## 查看变化

MD04：点击刷新 -> 最下面的 on/off 按钮被激活

点击 on -> End replenishment lead time 显示出来;

点击 off-> 隐藏显示

![image.png](assets/image-20211102181941-wc10gy0.png)

## MRP Area 级别的变化：

MRP area 级别没有设置此天数，但是因为 plant level 设置了，所以 MRP area 级别下也直接激活了 on/off

但是如果 MRP area 级别下设置了 planned delivery time, 那么它就不会读取 104 的值，以 MRP area 级别下的值为主。

![Machine generated alternative text:](assets/clip_image012-20211102181809-0k96bs4.jpg)

# 进阶讲解

## 如何设置 MRP Area 级别下的 Planned Delivery Time

MRP1 ->MRP area -> MRP2

需要注意的是天数后面的“consider palnned delivery time"一定要勾选，否则不起作用

![Machine generated alternative text:](assets/clip_image014-20211102181809-6nwtk9c.jpg)

## 建议 Planned Delivery Time 设置天数

一般来说，Planned delivery time 设置的规则是：比 SA 内 firm zone 的天数多一天。

例如：

> SA 内 firm zone = 78 天，
>
> Planned delivery time = 79 天

这样 78 天（含）的 Schedule Line 都是锁定状态，而 End Replenishment Lead Time 会显示在第 79 天的第一行，良好的区分出冻结区和非冻结区。

## SA 中的 Planned Delivery Time

需要特别注意的是，如开头所说，MM02 主数据中的 Planned Delivery Time 仅仅用于控制 MD04 中所显示的 End Replenishment Lead Time，对于实际的冻结区没有影响。

实际的冻结区控制在 SA item 的主数据当中。

Firm Zone 设置为 78, 则 78 天内(含)的 Schedule line 都会是*锁定状态。

Pl.Deliv.Time 设置为 79, 则新的 schedule line 会生成在第 79 天。

![Machine generated alternative text:](assets/clip_image018-20211102181809-tsi50ab.jpg)

# 文档信息

> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>
> 初次发布时间：2021-11-02
>
> 最后更新时间：2023-06-27
>
> 版本: V1.1

