﻿---
title: "AMS.ISSUE.Special Demand 被自动反冲"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# Special Demand 被自动反冲

## ⭐️ Key

因为AMS的需求逻辑, MD04内需求数字不与车号绑定, 只会优先消耗日期最旧的数字, 因此当Special Demand的日期在过去时, 会被优先消耗掉.

[📅20220124 Florian答复AMS Product Team答复, 及时更新SD日期就不会被反冲.](#20220124171645-7m3njq3)

![image](assets/image-20241213113517-4sgc529.png)SLBMA SLBPA用于LSO📅20241213 现在LSO已经上线, 可以自动issue

## 📆 Storyline

📅20211103 发现SD变少, 咨询赵京春

[20220413 回复 Please help to check these non baulos special demand plan no migrate to AMS and delete in IPT.msg](es://20220413%20回复%20Please%20help%20to%20check%20these%20non%20baulos%20special%20demand%20plan%20no%20migrate%20to%20AMS%20and%20delete%20in%20IPT.msg)

![image.png](assets/image-20220124171322-viaw73i.png)

📅20211105 Ticket `0059290597`

📅20220124 Florian答复AMS Product Team答复, 及时更新SD日期就不会被反冲.

[20220124 RE Tickets WP2WP3 for clarification .msg](es://20220124%20RE%20Tickets%20WP2WP3%20for%20clarification%20.msg)

## 🔗 Reference

### 涉及人员

[Florian Schneider](CRM/Florian%20Schneider.md), [赵京春](CRM/赵京春.md), [黄静](CRM/黄静.md)

‍

‍

