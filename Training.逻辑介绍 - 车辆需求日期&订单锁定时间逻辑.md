﻿---
title: "Training.逻辑介绍 - 车辆需求日期&订单锁定时间逻辑"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 车辆需求日期&订单锁定时间逻辑

# 逻辑

## AMS

1. 订单BOM在车辆销售之前，永远不锁定
2. 车辆 AS 开始后需求日期不再更新.
3. 系统根据下面的参数，自动推算所有零件的使用日期，推算到哪天，需求就显示在哪天。

    1. 生产节拍（根据产线的设计速度）
    2. 订单上线序（来自于ASF的珍珠链）
    3. 零件使用工位信息（来自于WP1的control cycle）
    4. 对于欠产或者超产的情况，不影响上述逻辑。每天晚上系统会根据当天的实际生产情况，以及待上线订单序列，重新计算所有零件的需求日期。
4. 对于ASF中手工Fix的车辆，会在系统中显示为Fix日期。[zip](es://20220816%20RE%20A%20000%20900%2097%2039_20220816_012619.zip)

## IPT

1. 上BS之前的订单，排在几号，零件需求就展示在几号
2. 上BS之后的订单，BOM锁定，零部件需求锁定，不随着订单排产日期的变化而变化
3. 欠产或超产情况下，因为涉及已经上了BS，零件需求日期不会发生任何变化

# Reference

明奎 ： [msg](es://20210901%20答复%20AMS车辆订单锁定时间.msg)

[李明奎反馈逻辑区别: 总结来说, AMS中BOM在车辆销售前不锁定. 系统根据节拍,序,工位来推算需求日期.msg es://20220127 答复 Meeting Minutes_H247 ...](.md#20220127154338-7uxwbd9)

‍

