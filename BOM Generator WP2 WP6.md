﻿---
title: "BOM Generator WP2 WP6"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# BOM Generator WP2 WP6

## 浠嬬粛

### NEW鏂规

[20240126 Overview BOM-Generator.pptx	](es://20240126%20Overview%20BOM-Generator.pptx)

The new BOM Generator replaces the Host BOM Generator

* TBE-Service instead of host programs
* Only necessary data being maintained in Dialog/P
* Progam logic in AmS

Implementation timeline

* New BOM Generator replaced Battery host solution in Tuscaloosa (End of 2022)
* Implementation in Kamenz plant (September 2023)
* Prototype in bodyshop plant Sindelfingen (Autumn 2023)
* Future projects:

  * Plant Hamburg
* - Tuscaloosa Multi Level BOMs

  * Plant Beijing
  * Tuscaloosa Body Shop

![image](assets/image-20240207143245-ounm37k.png)

> TABLE
>
> /DA0/4230_BGTRIG: Control table for BOM headers,
>
> /DA0/4230_BGCODE: Codes for BOM headers
>
> /DA0/4230_BGREC: Relationship of receivers and BOM headers  
> // For each BOM header, the receiver field determines what items will be assigned.  
> // Multilevel BOM creation will be achieved with iteration.
>
> ![image](assets/image-20240207144434-2pbssfq.png)鈥?>
> 浠ヤ笂涓変釜Table鍙湪((20240207144609-ie2m762 'TA: /DA0/4230_BK_MAINT -&gt; Maintenance transaction for the main BOM generator parameters'))缁存姢
>
> 鈥?>
> /DCI/RQI_MB_MAS: RQI model bOM
>
> /DA0/4230_INTORD: Internal TBE service orders (before sending out)
>
> /DA0/4300_ORDBOM: Received TBE service orders including components
>
> /DA0/4230_BOMRAW: BOM raw data table

> T-Code
>
> TA: /DA0/4230_BK_MAINT -> Maintenance transaction for the main BOM generator parameters
>
> TA: /DA0/4230_ORDER_CREA -> Creation of dummy orders
>
> TA: /DA0/4300_REQ_BOMEXP -> Send dummy orders to the TBE service
>
> SE38: /DA0/4230B_BOMRAWUPD -> Update of the raw data for BOM creation
>
> SE38: /DA0/4230_PEM_CREA  -> Create and change the change numbers (PEMs)

![image](assets/image-20240207145051-eoeousn.png)

### HOST鏂规(鏃?

[Battery BOM generation HOST solution.docx](es://Battery%20BOM%20generation%20HOST%20solution.docx)

[20211108_AmS_Baukasten_BOM_Battery_V3.pptx](es://20211108_AmS_Baukasten_BOM_Battery_V3.pptx)

IPT鏃舵湡鎵€鐢ㄧ殑TPL BOM涓嶄細杩涘叆AMS, 鑰屾槸鏀逛负Baukasten BOM.

The data is stored in AmSupply customer tables (/DA0/0060_DIBKPO, /DA0/0060_DIBKHD) 

> 姝ラ
>
> The receiver fields must be maintained in rec. parts list (BCTP) and rec. master data.  
>     rec. parts list:  This receiver field links the purchasing parts (U7/*U7) to the assembly/subassembly (U0)  
>     rec. master data: First receiver field field determines the header of each Baukasten, has to be set on each production/scrap relevant assembly/subassembly (U0)

鈥?

