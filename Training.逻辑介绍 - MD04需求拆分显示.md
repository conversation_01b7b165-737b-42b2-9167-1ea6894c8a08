﻿---
title: "Training.逻辑介绍 - MD04需求拆分显示"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
![image](https://picsum.photos/1600/300)

# 逻辑介绍 - MD04需求拆分显示

# 现象及原因描述

在 MD04 查看零件需求数量时, 发现零件需求在同一天内分为两行显示, 简单来说这是系统基于非整数值的 Dynamic Safety Time 而做的需求拆分, 实际上还要考虑工作日历, 班次时长等等.

![](assets/clip_image002-20211028191337-d0mqvgp.png)

# 简单举例

## Dynamic ST 设置为整数

![](assets/clip_image004-20211028191337-olnfx4x.png)

需求没有拆分现象.

![](assets/clip_image006-20211028191337-r64rapx.png)

## Dynamic ST 设置为 0.5

![](assets/clip_image008-20211028191337-uoa69yq.png)

系统需求大致拆分为 1:1.

![](assets/clip_image009-20211028191337-ooyai7o.png)

## Dynamic ST **设置为 0.9**

![](assets/clip_image011-20211028191337-m62sp8m.png)

需求大致按照 9:1 的比例拆分显示.

![](assets/clip_image013-20211028191337-js0p12o.png)

# 进阶讲解

以 8 小时工作时长为例, 白班夜班共 16 小时, 系统会将当日需求拆分为 16 个 Buckets.

例如需求数 284, 拆分后为:

|18|18|18|18|18|18|18|18|18|18|18|18|17|17|17|17|
| --| --| --| --| --| --| --| --| --| --| --| --| --| --| --| --|

当按 1:1 的比例拆分时, 系统取前 8 个 Buckets 为第一组, 后 8 个 Buckets 为第二组, 显示结果为 140 和 144

当按 1:9 比例拆分时, 系统取 16*0.9≈14 个 Buckets 为第一组, 后 2 个 Buckets 为第二组, 显示结果为 250 和 34

![](assets/clip_image015-20211028191337-wmas8m8.png)

# 文档信息

> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>
> 初次发布时间：2021-10-28
>
> 最后更新时间：2021-10-28
>
> 版本: V1.0

