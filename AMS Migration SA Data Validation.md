---
title: "AMS Migration SA Data Validation"
summary: "AMS迁移过程中SA数据验证工作"
date: "2021-04-25"
updated: "2021-04-29"
status: "completed"
tags:
  - "AMS"
  - "Migration"
  - "SA"
  - "数据验证"
category: "Project"
project: "AMS迁移"
---

# AMS Migration SA Data Validation

## ⭐️ Key

### 数据源

| T-Code                    | Content                                     | Process                                        | Note                                                                |
| ------------------------- | ------------------------------------------- | ---------------------------------------------- | ------------------------------------------------------------------- |
| Query - WP3_MIG- ZWP3_001 | Material Number<br />Vendor<br />SLOC<br /> | SLOC->MRP Area<br />标注Material with SL<br /> | 导出带有订单的零件清单                                              |
| /DFJ/10B_ROC_STOCK        | Material Number<br />MRP Area               |                                                | 导出有需求的零件清单                                                |
| /DFJ/19_MDCR              | SA Master & Source                          | SLOC->MRP Area                                 | 筛选出MDCR没有导出的零件清单<br />放入ZEKPO再执行一次避免遗漏<br /> |
| Query - ZEKPO             | SA Master & Source                          |                                                | 避免遗漏, 检查MDCR没有查到的零件SA                                  |
| /DFJ/08_MM17              | MRP Area Master                             |                                                |                                                                     |
| Query - ZWP3_002          | Contract Validation                         |                                                | 注意需要匹配<br />零件号+Vendor 信息<br />                          |

### 数据整合

[V206@MFA Material SA Validation List 20211116.tfl](es://V206@MFA%20Material%20SA%20Validation%20List%2020211116.tfl)

先导出没有MDCR的零件号, 放入ZEKPO中获取数据, 再放入tfl执行一遍, 补充MM17数据

### Excel操作

需要特别注意多货源问题

对照上一版本文件找出差异件, 标注新增零件

Buy-back 的 controller 维护成国产的。双货源的先改成进口的了。

## 📆 Storyline

📅20220613  [根据 Dennis 的邮件清理零件的 linefeeding数据](AMS%20Migration%20SA%20Data%20Validation/根据%20Dennis%20的邮件清理零件的%20linefeeding数据.md)

## 🔗 Reference

### 涉及人员

‍

‍
