---
title: "JIS 归档逻辑"
summary: "JIS数据归档机制的详细说明和操作逻辑"
date: "2022-04-27"
updated: "2022-04-27"
status: "completed"
tags:
  - "JIS"
  - "归档"
  - "数据管理"
  - "系统逻辑"
category: "Project"
project: "JIS系统优化"
---

# JIS 归档逻辑

📅20220427 JIS数据归档job开始, [<PERSON>](CRM/Martin%20Otto.md)解释逻辑: [20220427 AW ShunyiBDA Activation of JIS Archiving.msg](es://20220427%20AW%20ShunyiBDA%20Activation%20of%20JIS%20Archiving.msg)

> JIS Settlement Reports are archived if they were created more than 180 days ago  
> Job runs daily at 23:00 and directly does the archiving, deletion job is triggered afterwards
>
> JIS Call/JIS Preview  
> Data is archived (and copied into JIS archive tables) if OL-IST + last processing of all component groups is more than 180 days ago, vehicle is in status 7500 (or higher/deleted) and no GR is open (see below for details)
>
> Job runs daily at  
> 8:00 to do the actions ZOFN and ZODL, then triggers the actual historization/archiving/deletion with event /DA0/JIS_ARCHIVIERUNG_READY  
> (parameter M100)
>
> Data is still available in JIS transaction (except outdated JIS Preview) until final deletion happens (currently not scheduled as a job) as it is in JIS archive tables (/DA0/0149_*)

‍
