﻿---
title: "MD04有但ASN Query没有 - Inbound Delivery 未自动关联到Shipment"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# MD04有但ASN Query没有 - Inbound Delivery 未自动关联到Shipment

能在MD04中看到ASN，但是SQ00的一些Query中找不到。某些地方能够找到，比如`/DA0/0220_DLVRY_STAT`​​和`WP4_Q_ASN_LIST ASN List report for WP3`​​

经查Shipments中是空的。

![image](assets/image-20230103164931-8u390fi.png)

[孟伶莹](CRM/孟伶莹.md)提供解决方法：

1. 通过MB号找到iDoc Number
2. 通过iDoc Number找到Shipment Number和inbound delivery
3. T-Code `VT02N`​​，输入Shipment Number
4. ![image](assets/image-20230103165537-a2vb75h.png)
5. ![image](assets/image-20230103165546-xir3zaa.png)输入inbound delivery
6. Save

