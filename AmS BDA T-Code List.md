---
title: "AmS BDA T-Code List"
summary: "AmS BDA系统T-Code清单和功能说明"
date: "2021-03-09"
updated: "2023-05-23"
status: "in-progress"
tags:
  - "AmS"
  - "BDA"
  - "T-Code"
  - "系统功能"
category: "Project"
project: "AmS BDA系统"
---

# AmS BDA T-Code List


| T-Code                                                                                       | Descrption                                                                                                                              |
| -------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------- |
| [/DA0/DISCO](AmS%20BDA%20T-Code%20List/DA0%20DISCO.md)                                       |                                                                                                                                         |
| [SM37](AmS%20BDA%20T-Code%20List/SM37.md)                                                    |                                                                                                                                         |
| /DCI/RQI_VB_MAT_USE                                                                          | Check production NO & Material Number AmS                                                                                               |
| /DFJ/07B_RQI_MAT_USE                                                                         | Check production NO & Material Number IPT                                                                                               |
| MF47                                                                                         | 夏丰: 用于清理 COGI                                                                                                                     |
| [/DA0/2080_COV_PRDNO](#20210706181549-go0ifjo)                                               | 根据零件库存和生产序显示断点时间, 联动 `/DA0/0360A_FZGVB`​                                                                              |
| [/DA0/0050A_MATPB suppressed planned independent requirement](#20210713200756-ko1zpjo)<br /> | suppressed planned independent requirement                                                                                              |
| [/DA0/4010_CRMAT_LP](#20210715091740-0eek71n)<br />                                          | 创建 indirect 和 direct 辅料                                                                                                            |
| MM17                                                                                         | 批量需求 MRP Plant & Area 数据                                                                                                          |
| FCAL                                                                                         | [工厂日历维护 working calendar](AmS%20BDA%20T-Code%20List/SCAL%20工厂日历%20Working%20Calendar.md)                                      |
| /DA0/0050A_FHLBP                                                                             | 显示生产线 [Cutover Date](Projects/AmS@BDA/AMS.BDA.Training/AmS%20系统需求显示及计算逻辑/Cut%20over%20date%20共线车型只能有一个日期.md) |
| /DA0/4010_MMI_KB                                                                             | Table, 在这里可以组合 Controller Code 和 BZA 来让系统定义出不同的                                                                       |
| /DA0/0360A_FZGVB                                                                             | 零件相关车辆生产序, 具体数据内容不明                                                                                                    |
| /DA0/0050A_PBEBZV                                                                            | Run TBE file                                                                                                                            |
| /DA0/0010_PARTS_CHG                                                                          | Part change report /DFJ/07_RQI_BOM_CHK                                                                                                  |

批量修改数据

	/DA0/4010_DISPOMASS: 批量创建/修改MRP Area数据

	MEMASSSA: 批量创建/修改SA数据

JITOM: 查看JIS Call信息.

/DA0/0050A_BEDZL，Demand Source中，系统自动的 Cut over 会显示为 SE，郝金鹏手工维护的 Cut over 会显示为 AA。

/DA0/0145_REORD_OV, [AmS.BDA.JIS.Reorder](Projects/AmS@BDA/JIS/AmS.BDA.JIS.Reorder.md)  
​![image](assets/image-20231027151617-gsaaicu.png)​  

[/DA0/0220_DLVRY_STAT](AmS%20BDA%20T-Code%20List/DA00220_DLVRY_STAT.md), ASN 数据, [Anna-Lena Oesterle](CRM/Anna-Lena%20Oesterle.md) 提供用来替代 SQ00 中的 BLG Report

[ME22N](AmS%20BDA%20T-Code%20List/ME22N.md),修改SPO-Special Order数量。

/DA0/0080_LAB, 查看下载call off数据

/DA0/4010_MMI_KB，让系统根据组合 Controller Code 和 BZA来设置默认的[Referance Type](AmS%20BDA%20T-Code%20List/Referance%20Type.md)

/DA0/1120_LX03, 导出系统库存信息,范旭提供. 📅20211009 这个命令在 SM37 中会 Run 超长时间无法结束, 因为范旭推荐了 LX03.

/DA0/DISCO_ADMIN, 调整 DISCO 中的 default value。

[SU01](#20211004112441-9zp5p12), 查看用户 Role 权限

/DA0/2080_CHECK_APP, 查看零件使用位置. 📅20211105 测试发现新零件都是 AS4, 似乎无法通过这个命令判断.

[MD04](AmS%20BDA%20T-Code%20List/MD04.md)

[/DA0/0220_FANL](AmS%20BDA%20T-Code%20List/FANL.md), [显示 ASN 报错信息, 例如 ASN 对应的 SA 内没有 schedule line, 或是卸货点不匹配, source 被...](AmS%20BDA%20T-Code%20List/FANL.md#20211124133909-00i8dh2)

/DA0/2080_DCMARC，[操作文档 - 查询DISCO删除记录](Projects/AmS@BDA/AMS.BDA.Training/操作文档%20-%20查询DISCO删除记录.md)

MQ01,查看、修改零件Quota

/DCI/RQI_MB_MASTER，查看零件DIALOG信息

SU3，修改AMS日期显示格式

‍

ME57, 批量查看/删除Purchase Requisition [AmS_WI_删除Purchase requisitions_批量.mp4](es://AmS_WI_删除Purchase%20requisitions_批量.mp4)

ME5A， “Scope of List” = ALV，以表格形式查看Purchase Requisition

‍

📅20240428 /DA0/0080_DEL_BANF, 批量删除Purchase Requisition. 可以手工通过零件号或是Controller ID触发, 也可以放进JOB里面每天运行一遍. [TST_320305_02_ Delete BANF.xlsx](es://TST_320305_02_%20Delete%20BANF.xlsx).  
​![](assets/image-20240428153205-22w75j0.png)

随着Sprint41, 把这个命令的使用放进了常规权限里, 可以通过JOB放到后台运行了. 

* [X] [测试下JOB后自动发出的邮件内容](https://dida365.com/webapp/#p/0136418d9a15c8847f174fc7/tasks/662b2f8af78e3d0000000379)

📅20240805 吴立新和刘俊表示最好是用户自己创建而不是他们创建job。可以使用SBWP配置email来接收run的结果, 然后在SM36创建JOB的时候选择receipt, 但是个人尝试失败.

‍

📅20240428 /DA0/0080_MRP_BREAK, MRP Run失败报告.  
​![image](assets/image-20240428153212-5b7lnlo.png)​

‍

DA0/0020_SOB01，创建 special demand

/DA0/0290_AOR，[AOR Linefeeding](LSO/LSO%20go-live.md#20230201131229-2nfiu9z)

/DA0/0050A_BAULOSDEF，[Baulos Creation & Maintenance](LSO/LSO%20go-live.md#20230201144448-47z8zjn)

/DCI/GLO_WORKLIST, 可批量查询RFQ状态和时间，与IPT不同的是无法发出 RFQ, 因为 DISCO 到 L40 前这里没有 item这里状态 80 后, DISCO 中也会调整为 300, 但是 SA 不会生成, 而是等待后续 DISCO 数据的维护

/DA0/4200_FAB_LAYOUT, 维护 factory layout

![image](assets/image-20230214103706-4rf4s3p.png)​

/DCI/RQI_FC_COCKPIT, 查看 / 激活 TBE-P 数据

/DA0/0080_HALKAL, 维护MRP Area与工厂日历ID的联系

![image](assets/image-20230523084651-2r3m12o.png)

​[MB21,手工创建Transfer Reservation : 将 EX33 需求从 MRP_MFA 传回 Plant Level](Projects/AmS@BDA/Gaps%20&%20Issues/EX33%20-_%20Series%20Double%20Orderding.md#20210927152401-nuj4ca7)

# P02-02-01 Pre-activities  (Assignment model bom to production line, delivery type, MRP area, line station, master data of other product teams)

| /DA0/4220_BRAAFL     | Assign MS/PV to Production Line |
| -------------------- | ------------------------------- |
| /DA0/4220A_BRSTL2FL  | Assigning MS BOM to Line        |
| OMIZ                 | MRP Area                        |
| OX09                 | Storage locations               |
| /DCI/POM_CHKPT_MAINT | Checkpoints                     |

# Call Off相关 - ME84, MN11

ME84 批量发送零件call off。可以设置参数放入job chain自动发送call off。AMS中，触发call off之后iDoc先会被缓存起来，随着半小时一次的JOB集中发送出去。如果着急，可以通过ME9E手工触发。

在MN11中,针对sa类型可以对上述情况进行配置，不过COC目前坚持使用半小时一次的call off逻辑。 [RE SA migration 202301040852.zip](es://RE%20SA%20migration%20202301040852.zip)

# Role 权限 相关

T-code: `SUIM`​ 查看Role包含的T-code及反查，查看用户T-code权限

![image](assets/image-20230104101147-wb9atzl.png)![image](assets/image-20231114151451-pdfi6nl.png)![image](assets/image-20231114151555-tcogjjd.png)​​​

![image](assets/image-20230104101217-hanuep7.png)

T-code: `SU01`​ 查看用户Role

![Machine generated alternative text:](assets/clip_image001-20211004112502-nu8j24n.jpg)

[录屏 - OA申请AMS权限修改 - 20211108-20211118155042-luncge3.gif](es://录屏%20-%20OA申请AMS权限修改%20-%2020211108-20211118155042-luncge3.gif)

​Readonly 权限

```
/DA0/R_GRUND_ANZEIGE
AmSupply PSV100 - Viewer

//IPT中有单独的账号SCWTO11, 需要每半年激活一次
```

IPT: SM36权限

```
/DFJ/90_IT_SUPPORT
# 该权限比较大, 覆盖了很多命令, 包括SQ00

```

![image](assets/image-20231008173514-palwjze.png)​

# 盘点 Posting

IPT 下线边盘点: /DFJ/12D_IV_LINE_CNT

![image.png](assets/image-20211118132649-kpny8mc.png)

AMS 下线边盘点: /DA0/3400_PVB_KOR

Post 差异使用 MI20

张国斌: [20211126 答复 请教一下关于年终盘点记账日期的问题.msg](es://20211126%20答复%20请教一下关于年终盘点记账日期的问题.msg)

> 仓库：
>
> HUINV01 - Create HU Phys. Inventory Documents 创建
>
> HUINV02 - Change HU Phys. Inventory Document 录入
>
> HUINV05 - Post HU Phys. Inventory Differences 过账
>
> 线边:
>
> MI01: 创建
>
> MI04: 录入
>
> MI07: 过账

# /DA0/0050A_MATPB suppressed planned independent requirement

## ⭐️ Key

MRP 想要增加 BZA 信息

## 📆 Storyline

📅20210713 MRP 提出希望在报表里增加 BZA 信息.

咨询 [David Menk](CRM/David%20Menk.md) 这个命令是哪个工作包的

📅20210714 David 转给 WP2, 咨询其能否提供培训

‍

# /DA0/4010_CRMAT_LP

创建辅料零件号

`AW PSV Rehearsal- Material  master data checking - DA04010_CRMAT_LP .msg`

‍

# /DA0/0360A_FZGVB

![image.png](assets/image-20211009135004-c6d9tch.png)

‍

# 计算零件断点 /DA0/2080_COV_PRDNO

![image.jpg](assets/image-20210706181642-wuwff57.jpg)

📅20210701 因为该报告如果零件没有断点就不会显示结果, 因此咨询 WP2 是否有类似的报告可以显示零件的每个车的上线时间.

📅20210818 可以使用 [/DA0/0360A_FZGVB](#20210818180804-yizyyxn) 查看原始生产序

📅20211009 Martin 介绍逻辑 `Simple IT Request: 6100000253, /DA0/2080_COV_PRDNO not update`

![image.png](assets/image-20211009135343-g0xljcl.png)

‍

# /DA0/LP_MASS_CHANGE - 批量修改SA主数据

有权限但不能执行修改操作，需要通过`SM30`​选择维护`/DA0/2080_BER_SA`​然后在新的entry中Check所有boxes

# /DFJ/19D_LFD_MAINT - 维护Linefeed

[答复__Linefeed_Pflege-20210420111700-34yk7jx.msg](es://答复__Linefeed_Pflege-20210420111700-34yk7jx.msg)

吴迪 ：规划是试装阶段维护，sop都是运营的来做

‍

‍

/DA0/0140_JIS_REL, 📅20240715,JIS零件的主数据信息查询, 例如CGM, MRP MASTER, 等等
