﻿---
title: "Training.操作文档 - 查询车辆计划上线日期变动情况 - VELO"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 操作文档 - 查询车辆计划上线日期变动情况 - VELO

> 使用T-Code VELO查询同一车号在不同日期内的计划上线时间变动情况. 有助于了解车辆排产变化情况.

‍

# 操作步骤

## 数据准备

需要提前获取车辆的`Production Number`, 或是`External Vehicle Number`. 以及车型信息.

如果只有零件号, 可通过<<[ZIBVBMAT](操作文档%20-%20查询零件所用车型、车号、用量%20-%20ZPCVBMAT.md)>>查询其车号.

## VELO

T-Code : `VELO`

### 配置

初次使用时, 如果左侧的Vehicle Model内容为空, 请联系IT支持人员进行配置.

![image.png](assets/image-20220421110904-41ag3ie.png)

### 参数

单击选择需要查询的车型.

![image.png](assets/image-20220421111633-phqprcd.png)

输入`Production Number`或在`Vehicle Order Number`位置输入`External Vehicle Number`, 之后点击![image.png](assets/image-20220421111345-m905tmn.png)

![image.png](assets/image-20220421111304-jcwtfyp.png)

点击选择Detail页.

![image.png](assets/image-20220421111800-cpexo5s.png)

点击选择最下方的Vehicle History and Changes.

![image.png](assets/image-20220421111837-0njlivo.png)

随后选择查看Change Documents

![image.png](assets/image-20220421112006-whb534k.png)

## Report

### 数据筛选

报告中的信息比较多, 首先需要进行筛选. 

可以选择导出后在EXCEL中进行筛选, 或是直接在系统中进行筛选. 这里只介绍在系统中进行筛选的操作.

选中Table Name列, Table Key列, 以及Field Name列, 然后点击Set Filter按钮.![image.png](assets/image-20220421112309-9cla3ex.png)

![image.png](assets/image-20220421113008-vag5xh3.png)

分别输入或选择[/DCI/CCG_CHKPT。](Projects/AmS@BDA/数据获取/Table%20SE16N.md#20230718094543-dy6s1iy),`*0753*`​​, `Pdate`​​后,点击![image.png](assets/image-20220421112500-fz8jffi.png)执行.

![image.png](assets/image-20220421113124-iadao1v.png)

### 结果说明

在结果界面中可以查看到车辆的变动发生日期(1), 原计划上线日期(3), 以及变更后的计划上线日期(3).

![image.png](assets/image-20220421113340-i6yxeee.png)

如图例, 表示在3月4日的JOB Run中, 原本计划3月17日上线的车被重新安排在了3月18日.

# 常见问题解答

无

# 文档信息

> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>
> 初次发布时间：2022-04-15
>
> 最后更新时间：2022-04-21
>
> 版本: V1.1

‍

