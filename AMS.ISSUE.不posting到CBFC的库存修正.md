﻿---
title: "AMS.ISSUE.不posting到CBFC的库存修正"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# 不posting到CBFC的库存修正

•Table /DA0/0220_GRPMOV contains all MvT that are actively used per plant

•When a material movement must not be communicated to cbFC, then it needs to be assigned to the MovTyp GRP “DUMMY”

•

•WP1 creates a copy of MvT 501/502:

-953 In RD parts, 501

-954 Out RD parts, 502

-WP1 adds MvT 953/954 to table /DA0/0220_GRPMOV with MovTyp GRP “DUMMY”

•

•Roles:

-WP1 creates a new user role /DA0/S_ANLAUF_RD_TEILE (=Ramp-Up RD parts)

-WP1 creates a new IDM role for BDA/SHY: “AmSupply PSV100 - Ramp-Up RD parts IN/OUT”

•Required for PR/PO parts stock-managed in AmS

