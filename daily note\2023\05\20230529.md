# 20230529

# V214 EV line Re-structure

## ⭐️ Key

物流规划(BRD by 苏培举)提出, 在214 EV线取消库房, 使用214大线的库房, 因此104_AS1A下不再设置卸货点, 需求通过SOBSL传递到104_AS1进行订购.

![image](assets/image-20230927111312-tkqr8gq.png)​

### 主要文件

BRD: [214@EV BRD from BBAC Logistic PP(with signature).pdf](es://214@EV%20BRD%20from%20BBAC%20Logistic%20PP(with%20signature).pdf)

Solution:  [Solution_WH Structure MRA U40A in U40B.pptx](es://Solution_WH%20Structure%20MRA%20U40A%20in%20U40B.pptx) [2023-05-15_MRA1 140 Restructuring.pptx](es://2023-05-15_MRA1%20140%20Restructuring.pptx)

## 📆 Storyline

📅202304 苏培举提交BRD

📅20230530 需要MRP尽早创建MRP Area，维护V214专用件的SOBSL从104_AS1A到104_AS1  
[批量/单独操作MRP Area及SOBSL文档](es://20230530174321%20RE%20V214@EV%20-%20Weekly%20alignment%20meeting.msg):[A20_Training_添加MRP AREA_添加SOBSL.docx](es://A20_Training_添加MRP%20AREA_添加SOBSL.docx)  
补充: 📅20230927 进口组MRP如上操作了, 国产组MRP之后就RR有过一段时间讨论, 最后交由物料管理科操作创建MRP Area后他们维护SOBSL.

📅20230706 需要通知WTO卸货点211X停用的事情。  
[20230706141620 答复 V214@EV - Weekly alignment meeting - Inform WTO .msg](es://20230706141620%20答复%20V214@EV%20-%20Weekly%20alignment%20meeting%20-%20Inform%20WTO%20.msg)[此前一号库卸货点重新规划时是我通知的WTO](Projects/AmS@BDA/一号库系统规划%20-%20取消104订购%20转入BS2.md#20230113150902-xic2ulb)。  
​![image](assets/image-20230706141557-vakanvs.png)​

📅20230713 同李雷沟通后，没有删掉卸货点的计划，且MRP答复停用call off就行了，没有必要特别通知。  
[20230713101721 RE V214@EV - Weekly alignment meeting - Inform WTO .msg](es://20230713101721%20RE%20V214@EV%20-%20Weekly%20alignment%20meeting%20-%20Inform%20WTO%20.msg)

📅20230920 将上述结果转给李妍妍。但反馈还是需要发邮件，因此发邮件给Andreas和Mark和禄魁。  
[20230927113716 UNLP 211X should stop call off use after V213 EOP.msg](es://20230927113716%20UNLP%20211X%20should%20stop%20call%20off%20use%20after%20V213%20EOP.msg)

📅20230925 经沟通, 目前由李静维护MRP Area, MRP方面由孙剑楠维护SOBSL.  
[20230927131922 答复 V214 国产清单.msg](es://20230927131922%20答复%20V214%20国产清单.msg)

📅20231206 基于LCM和Local MRP的讨论, LCM照常进行IMS的维护, 后续MRP自行调整SPT.

📅20250227 [20250227 回复_ 小线应不能直接供货，JIS件除外  A2979009725.eml](es://20250227%20回复_%20小线应不能直接供货，JIS件除外%20%20A2979009725.eml), 在工作流程上的争议, 马红艳澄清.

‍

## 🔗 Reference

物流规划: [苏培举](daily%20note/2023/08/20230822/苏培举.md)

IT: [李雷](CRM/李雷.md) [吴立新](CRM/吴立新.md) [李妍妍](CRM/李妍妍.Lydia.md)

MRP: [禄魁](CRM/禄魁.md) [曹楠](daily%20note/2023/08/20230822/曹楠.md)

物料管理科: [后贝妮](CRM/后贝妮.md) [翟志斌](CRM/翟志斌.md) [李静](20230529/李静.md)

‍

# 进口集装箱部分收货 - Syncro Supply - SYSU

## ⭐️ Key

### Key Info

‍

### Issues

质量索赔期是FOB所以不受影响, 但如果Partial GR,则会延长零件的收货时间,从而导致超期.

‍

## 📆 Storyline

📅20230531 E2会议上，SHU和BDA各自展示集装箱部分收货的报告。

SHU报告：[20230411_ContainerYard_MixedULPs_one_Container.pptx](es://20230411_ContainerYard_MixedULPs_one_Container.pptx)

BDA报告：[MBCC container partial GR analysis_v4 .pptx](es://MBCC%20container%20partial%20GR%20analysis_v4%20.pptx)

> BDA厂区未上线SyncroSupply，堆场库存非可视，未收货零件在MD04中为到厂状态，可能误导MRP判断，影响调箱即时性
>
> 流程变更引起工作量和第三方成本增加（重复装车、卸车），第三方成本预计上升30%
>
> 卸货道口拥挤，卡车等待时间预计上升30%
>
> 进口件付款周期延长
>
> 堆场库存增加
>
> 零件多次装卸车，可能造成质量影响

E2建议BDA方面调研上线SYSU  [20230601144129 FW MBCC container partial GR analysis_v4.pptx.msg](es://20230601144129%20FW%20MBCC%20container%20partial%20GR%20analysis_v4.pptx.msg)

📅20230601 Meeting with 李淳，刘俊。需要BRD，通过DB才能获得时间预估。

> 顺义上线SYSU是随X294项目做TrailerYard，其费用在8万欧元，BDA这边做还要再加上AMS的费用，预估+5万欧元。
>
> 不支持IPT，堆场可能要分区域给两个系统
>
> ~~待确定是否支持垂直堆积的集装箱~~

📅20230605 顺义同事周博介绍系统

SYSU的缺点：

> 1. 价格：[顺义上线SYSU是随X294项目做TrailerYard，其费用在8万欧元，BDA这边做还要再加上AMS的费用，预估+5万欧元。](#20230601144242-ao77b48)
> 2. [不支持IPT，堆场可能要分区域给两个系统](#20230601144435-p38kj34)，每个卸货点都需要单独的物理位置

📅20231207 散文, 孙剑楠, 潘超, 郭彤, 开会介绍这个系统.

> INFO: 顺义也是世盟.
>
> 系统功能: 对堆场进行可视化的管理, 链接AMS系统, 从而能够通过AMS系统呼叫堆场的集装箱, 按优先级送货. 集装箱从而支持部分卸货.
>
> 支持JIS发动机流程, 需要SYSU.
>
> 需要: 了解世盟当前的堆场管理系统. 了解成本收益. 顺义世盟的费用.
>
> SYSU成本预估:
>
> SHU相关人员: 周博(运营), 刘润泽(系统)
>
> 增加的费用: 集装箱往返. 掏箱. 质量.
>
> 到了堆场或者港口就GR付款了.
>
> Heavy Box: 按时间自动送货收货. BDA专有. SHU没有这种.
>
> WIW账号.
>
> 空运件直接在库房收货但是需要在SYSU系统中不操作. 同一个shipment下一起收货.
>
> 跨location的箱子, 需要收在一个地方, 然后转库. 目前BDA的操作是什么? 而且看不到IPT Location的零件.

📅20231208 在郭彤的联系下, 散文和孙剑楠去MFA堆场和世盟沟通, 联系人是 [李中良](20230529/李中良.md) 经理 13821619839.

📅20231212 找到一个介绍JIS-P 电池在SYSU的ppt, [Interface Syncro-Supply - AmS.pptx](es://Interface%20Syncro-Supply%20-%20AmS.pptx)  
从文件看, 两者的联动主要在于:

> Trailer call-off based on production sequence (IB-Actual) – based on prod. No. of vehicle and Trailer ID send notification to Synchro Supply (driving order)

📅20231218 [20231218100134 答复 SYSU SMI费用明细.msg](es://20231218100134%20答复%20SYSU%20SMI费用明细.msg)

📅20240122 BDA这边的堆场capacity是600, 目前大概450, SHU这边的capacity大概是是120 ，目前数量在70~80个。

📅20240204 今天SMT会议上, SHU有个需求是让SYSU改善对混装集装箱的处理方式, 预计节约每个厂区每周1小时的工作时间.

📅20240304 E3会议上, Benz表示收集目前SYSU的缺点, 列出单子来给COC作为改善. 之后安排MRP和后贝妮开会.

📅20240411 因E3 Benz和冯总推动此事, 因此早会上进行了讨论. MRP 张丽娟坚持认为这个系统会导致停线, 而且SYSU上线后堆场会成为一个系统库房, 所以用户应该是管理科.  
因此再次安排SHU同事和管理科的同事进行介绍.

📅20240416

> SHU经验:
>
> 堆场也需要盘点
>
> OverDelivery 报错 @ CY
>
> 影响FIFO的计算
>
> 现在很少再发生空运分批运输的情况, 特别是李岩提到必须到齐了才能通过海关.

📅20240507 DemandBoard Result: 

> [转发_ NP-0506DB_PAM申请.msg](es://转发_%20NP-0506DB_PAM申请.msg)
>
> 刘俊: 该需求需物流同事进一步澄清BDA这边是否有关于堆场库存、物权转移、索赔等流程制度，确认有没有，若有，需要更新，若无，需要建立。另外，建议BRD的申请中增加会签人，因为财务，质量等部门都会直接受影响，作为直接干系人，需要这些部门会签。

📅20240521 几次会议后, 朱弈程提出在质量方面, 时间是FOB, 因此索赔期不受影响.  
​![image](assets/image-20240524151623-frg12hp.png)

📅20240618 德国COC方面反馈没有capacity做. `URGENT SyncroSupply@BDA CoC Quotation`​

📅20240708 meeting with Gese Moritz, 刘俊, 李淳, zhuyicheng, 孙剑楠

> [Moritz Gese](20230529/Moritz%20Gese.md)
>
> 	Partial function not used in SHU.
>
> 	提及所了解到的一些其他的增强.
>
> zhuyicheng: 我们只是copy, 不做增强
>
> Moritz Gese
>
> 	想知道目前的process
>
> 孙剑楠介绍process
>
> zhuyicheng: 再次强调我们只是copy

📅20240719 meeting: SYSU是否必须? 直接在AMS中实现提前收货是否可以? - Moritz Gese: Christian Benz说这个系统是only solution  
但是可以考虑先prepone AMS GR function.   
​![image](assets/image-20240719162129-9j049gt.png)​

📅20240722 [ Technical Review of SyncroSupply Rollout@BDA (SyncroSupply, AmSupply, BBAC IT &amp; BBAC BU) ](es://%20Technical%20Review%20of%20SyncroSupply%20Rollout@BDA%20(SyncroSupply,%20AmSupply,%20BBAC%20IT%20&%20BBAC%20BU)%20)朱姐确认, E3 Christian Benz要求一次性上线不要分开.

‍

‍

## 🔗 Reference

[张可馨](CRM/张可馨.md) [孙剑楠](20230529/孙剑楠.md)

[李淳](CRM/李淳.md) [刘俊](CRM/刘俊.md)

[Dirk Stutzbach](20230529/Dirk%20Stutzbach.md) [刘一辰](20230529/刘一辰.md) [周博](20230529/周博.md) [朱弈程](20230529/朱弈程.md)

[李中良](20230529/李中良.md)
