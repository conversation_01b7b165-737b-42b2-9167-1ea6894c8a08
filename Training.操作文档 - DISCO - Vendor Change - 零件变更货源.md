﻿---
title: "Training.操作文档 - DISCO - Vendor Change - 零件变更货源"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 操作文档 - DISCO - Vendor Change - 零件变更货源

> 通过 `/DA0/DISCO` 中的 `Vendor Change` 功能，可以更改或新建另一个供应商的 SA。并通过设置生效时间，实现供应商切换或是双货源同时供货（在不同的 MRP Area 下）等操作。
>
> 对于双货源在不同 MRP Area 下供货的情况，在添加新的供应商 SA 后需要继续进行 [UNLP Change](操作文档%20-%20DISCO%20-%20UNLP%20Change%20-%20零件变更卸货点.md) 操作来将 SA 的卸货点应用到新的 MRP Area 下。

## 操作步骤

### DISCO

T-Code：`/DA0/DISCO`，选择 Vendor Change 流程

![image](assets/image-20221124134450-739qxz6.png)

#### [100/110] Edit Worklist

点击 ![image](assets/image-20221122154904-r2zpncx.png) 创建任务，一般通过输入零件号创建任务。如果系统提示有黄灯、红灯不可创建任务，请参考系统最右侧的信息提示判断原因。

随后选中所有行，点击 ![image](assets/image-20221124134620-kfty463.png)

![image](assets/image-20221124134652-53xwyf0.png)

#### [140] Add Vendor

选中一行作为主数据的基础，点击 ![image](assets/image-20221124135707-j8ib6i6.png) 复制出一行新的。

选中新建出来的 Item 行，点击 ![image](assets/image-20221124135805-iod3com.png) 从采购系统拉取合同。这是有两种可能：

> - 状态【140】：已经获取新供应商合同，请进入到下一步 [[200] Determine Vendor](#200-determine-vendor) 继续。
> - 状态【120/125】：未成功获取新供应商合同

![image](assets/image-20221124140203-l1ckq1h.png)

未能获取供应商合同时：

1. 如果是进口件，请考虑是否需要发送 RFQ。点击 ![image](assets/image-20221124140609-fvcv9zk.png)。待 RFQ 完成后，再次点击 ![image](assets/image-20221124135805-iod3com.png) 从采购系统拉取合同。
2. 如果是国产件，请待采购完成国产合同的维护后再次点击 ![image](assets/image-20221124135805-iod3com.png) 从采购系统拉取合同。
3. 系统中存在多个有效合同，需要点击 ![image](assets/image-20221124140747-kombngp.png) 来选择一个供应商。

完成后任务状态为 140，继续进行下一步。

#### [200] Determine Vendor

在这一步中，需要决定继续生效的 SA。如果是切换的情况，则选择新供应商后点击 ![image](assets/image-20221124141017-zhdref4.png)。如果需要多个供应商都继续供货（多货源、买卖件等情况）则选中所有 SA 行后点击 ![image](assets/image-20221124141017-zhdref4.png)。

完成后任务状态变为 200，继续进行下一步。

#### [250] Maintain MRP Fields

这一步为维护零件相关主数据，例如 MRP Controller ID，Safety Time，等等。

首先点击 ![image](assets/image-20221122162555-onre7qj.png) 读取系统默认值。

随后通过 ![image](assets/image-20221122162622-g3r645i.png) 分别进行单行更改或是批量更改。本步骤必须进行操作，如果不需要进行任何更改，也需要进入修改界面然后点击确认。

完成后任务状态变为 250，继续进行下一步。

#### [310/311] Configure Change

这一步确定 SA 的生效/失效时间。

选中所有行，点击 ![image](assets/image-20221124141430-wc2kfv3.png)。

填写生效日期及供货比例。对于停用的供应商，供货比例填写 0，其余情况均填写 100。

![image](assets/image-20221124141441-f97jqjr.png)

完成后任务状态变为 310/311，继续进行下一步：

1. JIS 零件点击 ![image](assets/image-20221124141859-p7sbjuy.png)（请注意，类如 JIS 切/添 JIT SA 的情况下，也需要对旧 JIS SA 进行这一步操作）
2. JIT 零件可直接点 ![image](assets/image-20221124141911-wbxkapo.png)

#### [500] JIS Assign CGM

仅 JIS 相关零件需要本操作。

1. 如果接下来继续做卸货点变更，那么旧 SA 行和新的 JIT SA 行：统一点击 ![image](assets/image-20221124142131-4ax4mcd.png)，**不做 CGM 的变更**。CGM 的分配留到卸货点变更的步骤中进行。
2. 如果是 ECC 替代关系，则可以在这一步 Assign CGM。

随后进入 ![image](assets/image-20221122162814-0kxzydz.png) 步骤。

#### [900] Activate

进行最终的数据确认，如无问题，点击 ![image](assets/image-20221124142236-8lpw9q7.png) 确认激活。

完成后，如果激活日期是当日，则状态变为 900，如果激活日期是未来的某一天，则状态变为 600。

### MEQ1

在2025年AMS系统升级到HANA版本之后, 进一步强调了使用Quota控制零件供应商切换的功能。因此关闭Quota功能的方式有所调整。

|               旧版控制方法               |       新控制方法       |
| :--------------------------------------: | :--------------------: |
| 在MM02中将Quota Arrangement参数调整为'S' | 使用MEQ1删除Quota Item |

T-Code：`MEQ1`，输入零件号进入该零件的Quota配置界面

![2025-06-10_14-20-06](assets/2025-06-10_14-20-06.png)

点击![image-20250610152154019](assets/image-20250610152154019.png)

如图, 输入31.12.9999,确定,随后点击![image-20250610152245093](assets/image-20250610152245093.png)保存



![2025-06-10_14-27-09](assets/2025-06-10_14-27-09.png)

通过回车再次进入该零件的Quota配置界面, 选择旧的Quota行(所有非9999的行), 点击删除, 随后点击![image-20250610152245093](assets/image-20250610152245093.png)保存退出

![2025-06-10_14-29-59](assets/2025-06-10_14-29-59.png)



## 其他注意事项

请在进行相关操作前后，同其他部门相关同事做好充分的沟通工作。

## 常见问题解答

暂无

## 文档信息

> - 李天宇 ([<EMAIL>](mailto:<EMAIL>))
> - 初次发布时间：2020-10-16
> - 最后更新时间：2025-06-09
> - 版本: V1.4

