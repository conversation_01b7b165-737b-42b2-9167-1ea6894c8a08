﻿---
title: "物流现场事件追踪管理系统 - Logsnap"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# 物流现场事件追踪管理系统 - Logsnap

## ⭐️ Key

前身：[logticks-app-zcu39d](物流现场事件追踪管理系统%20-%20Logsnap/LogTicks%20App.md)

相关：[ecp-oa-application-form-adds-newly-added-zen4vu](物流现场事件追踪管理系统%20-%20Logsnap/ECP%20OA%20申请单新增.md)

文件位置：[Logsnap - 20230208](es://Logsnap%20-%2020230208)

## 📆 Storyline

📅20230202 接到康健邮件，开始以国内开发为目标重启[logticks-app-zcu39d](物流现场事件追踪管理系统%20-%20Logsnap/LogTicks%20App.md)需求。

> Information
>
> 1.使用该系统为BBAC内部人员（或有BBAC邮箱的外部人员）
>
> 2.使用终端为手机端
>
> 意向
>
> 使用企业微信解决
>
> Todo
>
> Log:
>
> 1.评估ROI
>
> 2.BRD重新提交
>
> IT：
>
> 1.咨询给有BBAC邮箱的外部人员开通企业微信账户可行性（数量50）
>
> 2.开发供应商产能是否足够

📅20230213 根据SHU需要50个账号，考虑BDA情况，MRA位置11个U area，算上4个ENG，2个MFA，共17个Area，每个区域20人，340人，报给刘俊。

📅20230308  
1 SHU的钱里面很多不是运营的成本，而是项目/物流规划的。  
2 根据ROI，BDA还需要增加9万  
3 能否就是用微信小程序  
4 安全部门正在用的？

📅20230331 听刘俊说，以前PP提过类似的，侯雪君去咨询了。

📅20230414 PP为自研系统，不方便拿给物流使用。雪君答复还是需要这个系统，但是目前ROI不行，所以状态搁置。

## 🔗 Reference

### 相关人员

[healthy-njiio](CRM/康健.md) [侯雪君](CRM/侯雪君.md)

[刘俊](CRM/刘俊.md) [潘超](CRM/潘超.md)

/

‍

