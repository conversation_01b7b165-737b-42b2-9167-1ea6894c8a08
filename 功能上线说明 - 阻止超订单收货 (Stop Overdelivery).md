﻿---
title: "功能上线说明 - 阻止超订单收货 (Stop Overdelivery)"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# 功能上线说明 - 阻止超订单收货 (Stop Overdelivery) 

# 📅 上线时间

2022 年 7 月 24 日

# 🕓 文档更新时间

2022 年 7 月 29 日

# 🧑‍🏭 主要涉及业务

收货

物料控制

物料管理

‍

# 💻 功能描述

该功能需求由物料控制科同事提交, 参考 IPT 已有功能开发. 目的在于当供应商发送的零件数量超过订单量时, 收货扫码的时候 Mobsy 上可以有报错信息提示并停止此 HU 收货.

# 🧾 功能前提条件

系统后台开启参数 `Stop Overdelivery`

![image](assets/image-20220725102349-jmcbk5t.png)

零件 SA 主数据中, 取消 `Unlimited` 勾选.

![image](assets/image-20220725102433-xlpqlec.png)

# ✅ 功能运行结果

系统会在收货时对已收货数量和零件订单数量进行对比.

如果出现超订单收货的情况, 系统会停止该 HU 的收货并在 Mobsy 上显示报错信息.

![image](assets/image-20220725102624-3lsegsl.png)

通过配置打印机, 可以实现相关信息的标签打印.

![image](assets/image-20220728103639-0co5gb2.png)

‍

