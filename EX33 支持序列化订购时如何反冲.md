﻿---
title: "EX33 支持序列化订购时如何反冲"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# EX33 支持序列化订购时如何反冲

# ⭐️ Key

> SA 生效前(或是 EX33 下订单前) AMS 中零件不会显示及反冲需求.
>
> ~~必要时可手工调整零件状态到 40 并维护 MRP Area.~~  零件在30模式开始不显示需求, 但是反冲. [20231221083351 回复 试装过程中EX33零件反冲的条件讨论 Discussion of EX33 parts  back-flash consumption .msg](es://20231221083351%20回复%20试装过程中EX33零件反冲的条件讨论%20Discussion%20of%20EX33%20parts%20%20back-flash%20consumption%20.msg)

![image.jpg](assets/image-20210623180047-8vkzi9n.jpg)

[timeline_EX33_support_series-20210623180408-ay1lihh.pptx](es://timeline_EX33_support_series-20210623180408-ay1lihh.pptx)

📅20210926 EX33 下过订单之后, 零件的状态就会变为 40, 需求跑在 plant level. 导致[ex33-series-double-orderding-1khsab](Projects/AmS@BDA/Gaps%20&%20Issues/EX33%20-_%20Series%20Double%20Orderding.md)![image.png](assets/image-20210926134602-h1vhdnx.png)​

补充📅20240805再次check  
​![image](assets/image-20240816153543-ywcjbc9.png)

​​

​​

## 解决方法

例如 A 247 900 12 15

![image.jpg](assets/image-20210706153855-67u4vhr.jpg)

MM02 中调整 Plant-sp.Matl Status 到 40

![image.jpg](assets/image-20210706154022-s7qp03x.jpg)

新增加一个 MRP Area 作为 master area。注意查看需求后面的描述，需要维护对应位置的 MRP Area。

![image.jpg](assets/image-20210709105858-pv71zuk.jpg)

![image.jpg](assets/image-20210706154438-6o68l2w.jpg)

填写必要的主数据。成功。

![image.jpg](assets/image-20210709105933-hbnsfxr.jpg)

# 📆 Storyline

📅20210617 邮件咨询Christoph

📅20210623 邮件咨询[David Menk](CRM/David%20Menk.md)

📅20210625 Dennis 手工调整 N910143010017 的状态为 40, 系统中出现了需求. [RE MRP controllers and demand creation .msg](es://RE%20MRP%20controllers%20and%20demand%20creation%20.msg)

📅20210709 自行测试， 成功。

📅20210713 杜芳提出, 批量添加 MRP Area, email [Dennis Kussmaul](CRM/Dennis%20Kussmaul.md)

📅20210720 [/DA0/4010_DISPOMASS](Projects/AmS@BDA/蓝图%20&%20流程/AmS%20BDA%20T-Code%20List.md#20231030142134-n8t811i)

![image.png](assets/image-20210720090444-zamnd46.png)

![image.png](assets/image-20210805142740-ii9d2if.png)

📅20210805 答复杜芳, 使用 MM17 批量修改 status, 使用 DISPOMASS 批量添加 MRP Area

‍

