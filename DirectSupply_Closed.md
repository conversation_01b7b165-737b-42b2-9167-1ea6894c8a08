﻿---
title: "DirectSupply_Closed"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# DirectSupply_Closed

## 猸愶笍 Key

[鐞嗚В涓篏SS鍜孏SS+骞惰鏈熼棿, 闇€瑕佺粰IPT鐨処doc澧炲姞瀛楁鏉ヨ繘琛屽尯鍒?](#20231030095744-s85ryia)

杩涘彛渚涘簲鍟嗙洿杩?BBAC, 鍜ㄨ MRP 涓嶆竻妤氭浜? 鍙嶉鏉庢烦涔嬪悗鏃犲悗缁?

## 馃搯 Storyline

馃搮20191205 鎺?IT 娑堟伅锛孎rank 姝ｅ湪浠?SC/WTO 鐨?DirectSC 椤圭洰杩涜娴嬭瘯锛岄渶瑕?BBAC 浜哄憳鎻愪緵涓€浜涗俊鎭繘琛岄厤鍚堛€?鍏堟壘鍒版潕宀╋紝琛ㄧず鏃犳硶閰嶅悎锛屽悗鎵惧埌鏂逛紵鑸€?鏂逛紵鑸挩璇?Frank 鍏蜂綋闇€瑕佷粈涔堟牱鐨勪俊鎭紝鏈 Frank 绛斿銆?
馃搮20200821 鏉庢烦鎻愬埌锛孌irect Supply 宸茬粡寮€濮嬪湪 BBAC 璇曠偣锛屽笇鏈涙壘鍒?BU 鐨勫弬涓庝汉 浜庢槸閭欢鑱旂郴鏂逛紵鑸拰鏉庡博 -鐩墠鏂逛紵鑸紤鍋囦腑

馃搮20200824 寰楀埌鏂逛紵鑸瓟澶嶄笉鐭ラ亾杩欎釜浜嬫儏鐨勫悗缁繘灞曪紝鍙嶉鏉庢烦. [绛斿_ 绛斿_ REMINDER_     Request for test support_24_8_2020.pdf](es://绛斿_%20绛斿_%20REMINDER_%20%20%20%20%20Request%20for%20test%20support_24_8_2020.pdf)

馃搮20210112 Frank缁欐柟浼熻埅鍙戜簡BRD鏂囦欢, 閲岄潰鎻忕粯涓?[BRD for IPT - GSS+ interface.msg](es://BRD%20for%20IPT%20-%20GSS+%20interface.msg)

> In future will we have for a long period of time parallel supply chains from trading house system GSS and GSS+ to BBAC China, Starting early 2021 with the first Direct Supply Chain containers in GSS+.
>
> For the DSC supply chain process from Germany and Europe SHPMNT IDocs from GSS+ should be received with an sender address 鈥?logical system (LS) IPT INT and Prod System. Currently onls GSS LS are known (G2K100 and G2P100).
>
> As we use the same  Partner LI / 90920400 for call offs and use the same ship2party 81900311 in both applications it would absolutely make sense, if we would create the GSS+ LS names in IPT for the SHPMNT Idocs, With that information we can uniquely identify the source of the messages, that would ease also the work of the support.

鐞嗚В涓篏SS鍜孏SS+骞惰鏈熼棿, 闇€瑕佺粰IPT鐨処doc澧炲姞瀛楁鏉ヨ繘琛屽尯鍒?

## 馃敆 Reference

[Frank Zipperle](CRM/Frank%20Zipperle.md)

[鏂逛紵鑸猐(CRM/鏂逛紵鑸?md)

[鏉庢烦](CRM/鏉庢烦.md)


