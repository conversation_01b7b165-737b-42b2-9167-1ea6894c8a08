---
title: "AMS.BDA.Migration.COGI Cleaning to 0"
summary: "AMS BDA迁移过程中COGI清零处理"
date: "2021-02-26"
updated: "2021-02-26"
status: "completed"
tags:
  - "AMS"
  - "BDA"
  - "Migration"
  - "COGI"
category: "Project"
project: "AMS迁移"
---

# AMS.BDA.Migration.COGI Cleaning to 0

## Summary (项目摘要)

本文档讨论了 AMS BDA 迁移项目中 COGI 无法清零的原因及处理方案。主要原因包括 COGI 随反冲随时生成，管理科无法短时间清空；以及 JIS 件 COGI 清理由供应商发起，物流和财务对 JIS 供应商追溯期无强制约束。物流运营部门致力于清零 COGI 并每周汇报无法清零的原因，但强调根本原因不在物流运营侧，需以无法清零为前提设计解决方案。针对 AMS 迁移，杨威接受 COGI 不迁移到 AMS 的方案，但要求详细解释 GR 影响。讨论中提及 MIGO 操作包含 SA 和 GR 信息，并倾向使用 GIPR 和 GRST 消息。基本方案已获接受，但仍需内部讨论成本中心等细节问题。AMS 将把 IPT COGI 中的原始成本对象复制为 IO 号，并在 cbFC 中进行 IO 与通用成本对象的映射。

## StoryLine (演进史)

📅2021-03-19: 使用 MIGO 在 AmS 中进行清理。AMS 会将 IPT COGI 中的原始成本对象复制为 IO 号，并在 cbFC 中进行 IO 与通用成本对象的映射。
📅2021-03-11: 杨威接受 COGI 不迁移到 AMS 的想法，但针对 GR 影响仍需详细解释。Stuetz 提到 MIGO 操作后包含 SA 和 GR 信息。杨威倾向 GIPR 和 GRST 消息。基本方案接受，但仍需内部讨论成本中心等细节问题。[Steward-Check: 行动项待办 - 未见成本中心等细节讨论结果] Stuetz 提出需要尽快进行测试。Florian 表示系统已准备好进行测试，并将立即测试。[Steward-Check: 行动项待办 - 未见测试结果]
📅2021-02-26: COGI 无法清零原因：COGI 随反冲随时生成；管理科无法短时间清空；JIS 件 COGI 清理由供应商发起，物流和财务对 JIS 供应商追溯期无强制约束。物流运营对 COGI 的态度是尽力清零，并每周汇报无法清楚的原因，但原因不在物流运营方面，需以无法清零为前提设计解决方案。

## Reference (参考资料)

主要文件:

- [答复 AMS 项目 COGI 处理问题.eml](assets/20210226154303-0swy1kf-答复_AMS项目COGI处理问题.eml)
- [系统中残留的 COGI 数量.msg](assets/RE__COGI_clearing_topic_before_migration_from_IPT_to_AMS-20210425171428-de67gmy.msg)
- [答复\__meeting_minutes_for_COGI_clearance_topic_on_18th_of_Mar_.msg](assets/答复__meeting_minutes_for_COGI_clearance_topic_on_18th_of_Mar_-20210319135738-aijzkcz.msg)
- [Meeting Minutes - AmS Migration Concept 2.0](assets/20210226144115-1l4xrhk-Re_AmSupply_data_freeze_alignment.eml)

核心人员:

- 赵绍昆 (采购)
- 董硕
- 杨威
- Stuetz
- Florian

相关图片:

- ![image.jpg](assets/image-20210319135805-r449idy.jpg)
