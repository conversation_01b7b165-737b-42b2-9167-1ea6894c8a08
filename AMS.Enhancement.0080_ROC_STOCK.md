﻿---
title: "AMS.Enhancement.0080_ROC_STOCK"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# /DA0/0080_ROC_STOCK - RoC Stock Report 问题追踪

## 摘要

本文档追踪 `/DA0/0080_ROC_STOCK - RoC Stock Report` 命令导出数据异常的问题。

核心问题是：
零件 `A 000 998 10 07` 在 104_BS2 (MRA2 装焊) 是国产货源，但导出供应商代码为进口。
零件 `A 000 998 10 07` 在 104_BS4 (MFA 装焊) 的 Material Requirement Planning Code 是 26S，但导出为 32S。

关键结论与状态:
系统调整建议: Supplier 按 source 中相同 mrp area 下生效的来，Controller 按 mrp area 下的主数据来。
当前状态: 已创建工单，并已纳入开发计划，等待下一个迭代的 Sprint 测试。

## 时间线

📅2025-04-28: 付近近 (Fu Jinjin) 提出 `/DA0/0080_ROC_STOCK - RoC Stock Report` 命令导出数据问题。具体表现为零件 `A 000 998 10 07` 的供应商代码和 Material Requirement Planning Code 信息与系统不符。
📅2025-04-29: 李天宇 (Li Tianyu) 建议系统调整方案：Supplier 字段应按 source 中相同 mrp area 下生效的来，Controller 字段应按 mrp area 下的主数据来。
📅2025-05-07: 吴立新 (Wu Lixin) 回复已为该请求创建工单，并等待 Central Operations Control Level 2 team 的反馈。任何进展将在此更新。
📅2025-06-20: 吴立新 (Wu Lixin) 更新：该问题已安排到计划中，下个开发迭代的 Sprint 测试时将通知相关人员。

## 参考

相关命令/系统: `/DA0/0080_ROC_STOCK - RoC Stock Report`
相关团队: COC Level 2 team
核心人员:
    付近近 (Fu Jinjin) <<EMAIL>>
    吴立新 (Wu Lixin) <<EMAIL>> - 第三方凯捷
    李天宇 (Li Tianyu) <<EMAIL>>
    刘俊 (Liu Jun) <<EMAIL>>

