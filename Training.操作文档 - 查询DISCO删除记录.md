﻿---
title: "Training.操作文档 - 查询DISCO删除记录"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 操作文档 - 查询DISCO删除记录

> 在 SE16 中通过零件号在 Table [/DA0/2080_DCMARC](Projects/AmS@BDA/数据获取/Table%20SE16N.md#20220323155204-au9ngbq)​ 中找到 DISCO Task No.，之后在通过 Task No 在 Table [/DA0/2080_DCSUPP](Projects/AmS@BDA/数据获取/Table%20SE16N.md#20220323155204-au9ngbq)​ 中找到该 Task 的操作及删除记录。

# 操作步骤

## 根据零件号查找 Task No.

T-Code:`SE16`

Table name: `/DA0/2080_DCMARC`

![Machine generated alternative text:](assets/clip_image002-20211125140852-dif3x1f.jpg)

输入零件号,执行。

![Machine generated alternative text:](assets/clip_image004-20211125140852-6bljyty.jpg)

双击零件号

![Machine generated alternative text:](assets/clip_image006-20211125140852-g9xpq1p.jpg)

可以查到如下的详细信息，及 Task No.

![Machine generated alternative text:](assets/clip_image008-20211125140852-958ebpf.jpg)

## 通过 Task No. 查找更多信息

T-Code:`SE16`

Table name: `/DA0/2080_DCSUPP`

![Machine generated alternative text:](assets/clip_image010-20211125140852-5q1ldre.jpg)

输入 Task No.

![image.png](assets/image-20211125140939-s8ral5e.png)

双击

![image.png](assets/image-20211125141013-e5zfllf.png)

可以查到详细信息：

Create date/ created by

LOEK: `X`  则表示被删除了

![image.png](assets/image-20211125141026-hfn6v5a.png)

# 文档信息

> 李妍妍 (<EMAIL>)
>
> 初次发布时间：2021-11-25
>
> 最后更新时间：2021-11-25
>
> 版本: V1.0

‍

