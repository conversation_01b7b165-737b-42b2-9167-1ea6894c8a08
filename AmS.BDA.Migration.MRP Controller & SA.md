﻿---
title: "AmS.BDA.Migration.MRP Controller & SA"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# AmS.BDA.Migration.MRP Controller & SA

# ⭐️ Key

## Controller ID Mapping in IPT

MRP同意在5月底前变更所有零件的controller到AMS结构.

需要在working level继续讨论具体的变更方式.

零件清单:[汇总到Material Data - MRP relevent - simplified 20210429.xlsx 发送给MRP](#20210429163403-55ay9rq)

Mapping 关系

> [20201210MRPControllerAMSBDA.xlsx](assets/<EMAIL>)

‍

## Quality Checking

[Data Validation - GitMind](https://gitmind.cn/app/doc/543fd5ce588c62d09268b7634094b79c)

![image.jpg](assets/image-20210526194226-8v9a5jg.jpg)

### SA Validation

📅20210629 准备升级用 ppt, IT 要求增加 Deletion Flag, 需要同 Ruesetem 沟通, 同 MRP 讨论

`Data Clean - SA Validation - Deletion Flag - 20210629.pptx`

#### Checking steps

##### Source - SM37

SQ01-PBID_PBED_MRP: MRP_5*

ZWP3_004 SA information

ZWP3_002 Contract information, 注意需要匹配 Vendor 信息

##### Source - Manual

Tableau 导出 `缺少MDCR`

MDCR: MRP_5*, CNB1, 制作增量 MDCR 文件

Tableaeu 导出 `MM17 Data`

MM17: CNB1 - SM37 时间太长, 改为手工项, 通过 tfl 输出零件清单, 导出 MM17, 然后再放入 tfl

~~Tableaeu 导出 check for V206~~

 ~~/DCI/RQI_FC_COCKPIT : 检查 22 年 4-5 月独有零件的需求, 排除 V206 专用件.(理论上 V206 专用件应该需求跑在 V206@MFA)~~

~~SQ01-VMS-ZMODULE 可以查看零件的车型信息. 清单应当除去 ES 代码和空格.~~

剔除 BGSL 和 BGSR, BGSL/R 须在 MRA 冻结区前迁移

‍

##### Excel

Migration

Remark

UnChecked SA `=COUNTIFS(C:C,C3,N:N,"",G:G,G3)`

Updated

remark

‍

1. 筛选 SA Valid To <202110, 标注 No, 检查 Unchecked 是否=0
2. 筛选 BZA 为 L1, L7 L0 的零件, 标注 **No Migration**, Remark 为 **In House**
3. 筛选 Vendor, 7 开头的标注 **No Migration**, Remark 为 **In House**, 注意 Remark 的标注
4. 筛选 Total SA = 1, 标注 **Yes**, 备注无 Call off 的 SA 需要 MRP 检查
5. 筛选 Total SA = 2, Area SA = 2, Unchecked = 2, Blocked, 标注 **No**, 检查 Unchecked 是否=
6. 筛选 Total SA = 2, Area SA = 2, Unchecked = 2, Call off = No, 标注 **No**, 检查 Unchecked 是否=1
7. 筛选 Total SA = 2, Unchecked = 1, 标注 **Yes**.
8. 筛选 Total SA = 3, Area SA > 1, Unchecked > 1, Blocked, 标注 **No**, 检查 Unchecked 是否=1
9. 筛选 Total SA = 4, Area SA > 1, Unchecked > 1, Blocked, 标注 **No**, 检查 Unchecked 是否=1
10. 检查一遍 W 列有文字备注的零件
11. 筛选 SA 号为空的零件, 标注 **TBD**, 检查在其他位置是否存在 SA, 发送给 MRP 要求检查. (Sheet `MRP checking list`)
12. 单独检查 BGSF 位置的 SA

‍

需要特别注意多货源问题

标注新增零件

对照上一版本文件找出差异件

buy-back 的 controller 维护成国产的。双货源的先改成进口的了。

‍

## 多货源零件处理

from 张嘉

A 005 990 47 50  
N 000000 004667  
N 000000 008721

A 000 905 59 12 `20211213 答复 IPT V206@MFA 位置缺少 SA Item.msg`

## 买卖件处理

### 零件清单

买卖件 BuyBack

### 主数据维护问题

需要进口SA回来之后才能维护国产SA, 否则进口SA无法进入系统

如果进口RFQ回来前就维护了国产SA, 则需要采购同事维护下系统.

20210326, AMS 中 DISCO 在 Determine vendor 步骤中可以选择获取 globus contract 还是 GSS contract, 因此应该没有这个问题了

### 迁移

BGSF 位置迁移到 326X. JIS 正常创建到 3J0X

📅20211005 因为只迁移 85 天数据，因此手动在 AMS 326X 卸货点码放后续订单，并手工删除 IPT 中 85 天后的订单。

‍

## 冲压零件迁移

![image.png](assets/image-20210907153736-a1tg7sp.png)

‍

## AS BS 通用件处理

[RE MRP ID Changing in IPT Progress.msg](es://RE%20MRP%20ID%20Changing%20in%20IPT%20Progress.msg)

SA 迁移到 BS4 位置，AS4 位置的需求通过在 AS4 区域设置 SB 转移到 BS4。Safety stock 也码放在 BS4 位置。

[20211005 AS  BS 通用件手工迁移完毕 - A 004 990 46 50.msg](es://20211005%20AS%20%20BS%20通用件手工迁移完毕%20-%20A%20004%20990%2046%2050.msg)

[AMSJ AMSR](Projects/AmS@BDA/AMS.BDA.Training/AMSJ%20AMSR.md)

‍

修复方法： [20230911144744 答复 A0019908000漏反冲问题协助.msg](es://20230911144744%20答复%20A0019908000漏反冲问题协助.msg)

1. 修改错误的AMSR
2. 或者 修改Deviating MRP Controller

    1. 检查物料主数据工厂级别MRP controller 是BS 还是AS
    2. 查Dialog 数据，通过在DISCO中检查"Display DIALOG usages from MMI", 或直接Dialog 中查都可以，找出那些与工厂级别属性不一样的model Part ID ,工厂级别定义的是BS, 找出AS对应的Model ID
    3. 通知Docu 加上Deviating MRP controller
3. 在[/DA0/4010_CHECK_AMSJ](Projects/AmS@BDA/AMS.BDA.Training/AMSJ%20AMSR.md#20230309091252-8ae71s0)中检查AMSJ/R的传输情况  
    ​![image](assets/image-20240520103039-isnptjk.png)​

# 📆 Storyline

20201214

[20201210MRPControllerAMSBDA.xlsx](assets/<EMAIL>)

20201215 冲压PS[汪锐](CRM/汪锐.md)答复分配没问题[email](assets/20201218213352-0k4j5tm-答复_%20Re[2]_%20MRP%20Controller%20AMS@BDA%20-%**********.xlsx.eml)

202012182127 根据[Christoph Muench](CRM/Christoph%20Muench.md)的要求进行了调整。 仍有 Open 问题

[email with Christoph include updated file](assets/20201218212735-kx96n38-Re_%20AW_%20AW_%20AW_%20AW_%20AW_%20MRP%20Controller%20AMS@BDA%20-%**********.xlsx.eml)

> 1. Cl* and CL* are two different controller ID? I only know Cl*
> 2. YEN is for the engine ZB. will there be a special ID for ZB parts in AmS?
> 3. Those ID I do not recognize.
>
> `, 00F, 31, BA1, L8, LC1, LC4, LC5, LC6, LE0, LE1

Q for MRP: 这些旧的 MRP ID 如何处理？

M31 M41 M52 M80 M90

[email ask MRP 如何处理旧 ID](assets/20201218213042-wlyw62z-Re_%20AW_%20AW_%20AW_%20AW_%20AW_%20MRP%20Controller%20AMS@BDA%20-%**********.xlsx.eml)

S10 是软件 [license-material-25wmbx](Projects/AmS@BDA/蓝图%20&%20流程/License%20Material.md) 的 IPT Controller ID

20210324 MRP同意进行所有零件的变更

![image.jpg](assets/image-20210324100526-to14f2q.jpg)

20210425 准备数据提供给MRP

1. MM17 MRP Area
2. MM17 CNB1
3. MM17 SA

   保留Material, MRP Area, Material Description, MRPCn, Rounding Val., SafetyTime, PDT

   基于1，2，3进行筛选，MRPCn = Null & YYY & ZZZ & YEN & 666，Material = Null, MRP Area = PRS_*, 通过`Data preparation.tfl` 输出`初步MM17得到的PN list.hyper` , 47332个PN
4. SQ01 open schedule line
   from 20210601-20211231 [query.jpg](assets/image-20210426171604-k576f2v.jpg)
   拆分运行

   1. A21* `DONE`
   2. A29* A28* A27* A26* A25*
   3. A2* 排除A21* A29* A28* A27* A26* A25*
   4. A1* `DONE`
   5. A* 排除A1* 和A2* `DONE`
   6. N*, R*  `DONE`
5. SQ01 WH Stock -> `20210425 PN has stock in today SQ01 WH.csv`
6. Open ASN `20210425 ASN Open.txt`
7. MDCR SA

通过`combing data.tfl`整合数据, 输出`combined material info`

SA和MRP Area有重复, 需要检查

MM17的原始数据包含多条重复的item, 检查`Data preparation.tfl`, 更新OFFA的Storage Location数据, 确保所有的SL都有MRP Area信息

![image.jpg](assets/image-20210428200142-0pew1ym.jpg)

预计发送给MRP两个文件, 1只有库存, 附带last mvt日期. 另一个则是MRP relevent零件清单.

合并`MM17 SA.XLSX` 和`Material SA with Scheduleline.hyper` , 为由schedueline的SA附加更多信息. 通过其storage location和OFFA文件, 为其附加MRP Area信息.

根据storage location, 分开JIS和non JIS

筛选只有库存无在途和订单的零件, 检查其库存的最后一次收货时间.

汇总到`Material Data - MRP relevent - simplified 20210429.xlsx` 发送给MRP

202105061524 Meeting with 张嘉 逯魁 付近近

添加Vendor信息, AS/BS/ENG/BP

只有库存的零件增加主数据信息

5月18日, 完成初步的MRP mapping

5月21日, 使用最新数据更新list(MRP&lity)

5月25日, 系统调整完毕

5月28日, IT反馈

通过Call Off Test Run获取SA的vendor信息

20210519 MRP 提供Controller ID Mapping. 但是具体的零件级别因为人力紧张需要晚些更改.

根据MRA推迟的信息, MRP会先更改MFA相关零件.

20210521 Controller ID Mapping 提供给Project

[国产MRP反馈.msg](assets/RE__Material_List_to_Check_the_MRP_Controller_ID_in_IPT-20210520110455-a2plkyd.msg)

[进口MRP反馈.msg](assets/MRPC分配-20210521132558-vm2c0en.msg)

20210526 万杨在IPT中创建ID完毕

开始筛选零件清单, MFA [Data Validation - GitMind](https://gitmind.cn/app/doc/543fd5ce588c62d09268b7634094b79c)

导出8月-12月的零件需求(MRP_5*)

其中包含MRP_5位置的Controller信息``​`Demand Sq01 - 20210526.XLSX`

通过Tableau进行汇总

需要检查对应的SA, 通过MM17导出上述零件的SA号，保留MRP_5* ``​`MM17 SA Simplifed - 20210526.xlsx`

Special Cares

存在多个SA``​`存在多个SA.xlsx`, 需要标注Delete Flag

色码零件

外库324，723，721  ULP需要重新分配位置

没有SA的零件

JIS是否存在装焊

根据今日MB52库存数据，筛选出有库存无需求的零件

20210531 list to Christoph

万杨同Christoph沟通后，会由Christoph去做变更的操作。已发送。

[AMS_MRP_controller.msg](assets/AMS_MRP_controller-20210531173741-5owrnlv.msg)

20210601 清单更新后发送给Christoph

剩余零件添加 Vendor 和 BZA 信息, 提供给 MRP 检查

📅20210616 `N 000000 008345`, `A 001 995 93 77` 双货源. `A 000 998 97 05` 进口切国产

[答复__确认后续是否使用.msg](assets/答复__确认后续是否使用-20210622141417-4ikm9fg.msg)

[答复__确认后续是否使用.msg](assets/答复__确认后续是否使用-20210622141554-iwebod6.msg)

📅20210621 `A 177 401 06 00         7X21`, 买卖件，还未完成进口RFQ

[答复__A_177_401_06_00_________7X21.msg](assets/答复__A_177_401_06_00_________7X21-20210621131107-8lq1nva.msg)

📅20210622 `N 910105 010003`, 进口转国产

[答复__N_910105_010003.msg](assets/答复__N_910105_010003-20210622131417-8286u05.msg)

📅20210720 设置冲压 PS 零件的 controller 为 00S

![image.png](assets/image-20210720093750-fwpvujo.png)

📅20210926 A 247 860 08 00 在 DIALOG 中将会切换到 L8, 迁移时遇到困难, 最后选择手工创建.

![image.png](assets/image-20210926161842-mgsg8h4.png)

![image.png](assets/image-20210926161920-qahzv1k.png)

📅20210930 检查 GSS 表示有订单, 但不再 scope 里面的零件

A0005003800	302	delete  
A0009903662	302	delete  
A1679003125	302	NO SL  
A1679008025	302	5500004491  
A1679008125	302	5500004492  
A1679008225	302	5500004493  
A2058320500	302	delete  
A2475051200	302	added in "new"  
A2479001516	302	NO SL  
A2479001616	302	NO SL

要求 MRP 同 WTO 沟通.`20211001 回复 Missing SA in AMS for IPTGSS materials.msg`

