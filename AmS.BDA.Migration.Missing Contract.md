﻿---
title: "AmS.BDA.Migration.Missing Contract"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# AmS.BDA.Migration.Missing Contract

## ⭐️ Key

[Material_missing_valid_contract-20210602175909-k3bklck.xlsx](es://Material_missing_valid_contract-20210602175909-k3bklck.xlsx)

迁移时发现部分零件缺少有效日期合同,经过同采购部门沟通后,[邓红梅](CRM/邓红梅.md) L4 作为接口,协助更新 valid contract

### 缺少有效合同问题

GLobus 数据传输 IPT 失败导致 IPT 中没有 Valid 合同但是 Globus 中存在正常合同。需要 IT 的同事定期检查 red idoc

[20210730 答复 零件缺乏有效合同无法迁移到 AmSupply.msg](es://20210730%20答复%20零件缺乏有效合同无法迁移到%20AmSupply.msg)

王小燕咨询 HelpDeck，但是未得到答复。

同吴立新商量，因为 AMS 是从 Globus 重新抓取数字，而且本例是 MRA 零件，不因此不会对数据迁移造成影响。今后检查 Contract Validation 时需要使用 PSV/KSVs 数据复查。

[📅20210930 topic close](#20210926161224-nmypuh6), P&S 已更新已知缺失的合同.部分未传入 IPT/PSV 的由吴立新和刘俊邮件咨询 cbfc 相关数据.`20211001 cbFC SA 号码匹配.msg`

‍

## 📆 Storyline

📅20210512

开始检查KSV内迁移数据与IPT差异.

使用[amsbdamigrationmrp-controller-axqas](AmS.BDA.Migration.MRP%20Controller%20&%20SA.md)中的零件清单 `Material Data - MRP relevent - simplified 20210429.xlsx` 制作 `Material_missing_valid_contract-20210602175909-k3bklck.xlsx`

[20210425 准备数据提供给MRP](AmS.BDA.Migration.MRP%20Controller%20&%20SA.md#20210425111619-r3ii2cr)

📅20210518 检查零件号 missing contract 问题, 邮件发送给王晓燕.

经 PS 检查部分零件在 Globus 中存在有效合同.

> randomly pick two materials to P.S.  A 000 982 25 10 and A 243 610 63 00.
>
> Base on their feedback, Part1 has valid formal contract in  
> Globus.
>
> Part2 has a temp-contract, able to order material in IPT.
>
> ![](assets/clip_image002-20210520151500-8c1k4s3.jpg)
>
> ![](assets/clip_image004-20210520151501-va3rufa.jpg)
>
> So **from P.S point of view they have valid contract in the
> Globus.**
>
> Before we have further discussion with P.S, I suggest having  
> this issue cleared from IT side, why the two system are showing different  
> information.
>
> Because AmS do not recognize the temp-contract? or data transfer  
> has problem? Need IT clear first.

📅20210520

补充 4 个零件发给 PS 检查. 个人猜测其均为估价合同 2020 未更新 2021.

📅20210602 同进口P&S开会沟通. 

国旭, [王卿](CRM/王卿.md), 刘俊

根据国旭检查系统, 确实处于未续期状态. [答复__Globus_Contracts_Valid_Date_Renew.msg](assets/答复__Globus_Contracts_Valid_Date_Renew-20210602175139-upyckv0.msg)

| Material                     | Last update | Valid to   | 合同价                     | 情况                                   |
| ---------------------------- | ----------- | ---------- | -------------------------- | -------------------------------------- |
| A 177 680 81 06              | 2020/12/10  | 2020/12/31 |                            | 2021无正式价格                         |
| A 243 342 02 00              | 2020/8/31   | 2020/12/31 | 2.57/ST 20210101-20210303  | Pre-series价格，仅根据财务需要手工维护 |
| A 243 545 10 00              | 2020/5/25   | 2020/12/31 | 1.20/ST 20210101-20210303  | Pre-series价格，仅根据财务需要手工维护 |
| A 099 760 72 00         9799 | 2020/4/27   | 2020/12/31 | 67.96/ST 20210101-20211231 | 待审批                                 |

复查系统MB51记录, 确实一直在正常收货.

后续: P&S同事会就前三个件检查, 为什么没有合同信息, 是否已经影响付款. 物流会复查零件清单, 之后提供给P&S

📅20210602 获得Globus信息查询反馈.

根据张丽丽反馈, Globus不支持批量信息查询, 因此无法查询采购员和合同状态信息. 将升级给Markus支持.

![image.jpg](assets/image-20210602175644-xo9iw6l.jpg)

📅20210604 进口零件合同大幅更新, 剩余13个发给国旭和王卿

`回复__Globus_Contracts_Valid_Date_Renew-20210604231903-lw6xfsk.msg`

吴立新创建Query用来查询IPT中的零件buyer信息.

筛选出国产零件邮件发给采购. 有7个零件IPT中没有Buyer信息, `<EMAIL>` 提示邮箱已失效.

📅20210608 依要求把包括 MRA 在内的 missing contract 零件清单发送给采购同事。

`回复__零件缺乏有效合同无法迁移到AmSupply-20210610112858-hu3jp5r.msg`

需要咨询他们的 timeline

📅20210616

`Contract_Check_MFA_-20210615-20210616163422-7k90sfb.xlsx`

定期更新表格发给 PS 进行维护 

📅20210630

发现下面非多货源零件被更新了"错误"的 vendor. 添加到 miss contract 列表中. 咨询付近近是否有故事.

A 177 540 08 42,	A 177 540 09 42,	A 177 540 11 42

在用安通林 SA15692248, 但是采购 2021 年更新了 莱尼 SA15678079

📅20210926 Ali 作为接口人

📅20210930 topic close

‍

