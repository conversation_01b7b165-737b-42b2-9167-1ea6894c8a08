﻿---
title: "Training.操作文档 - 发动机总成号主数据维护 - AMS & IPT"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "培训"
  - "操作文档"

category: "Training"
project: "培训文档"
---
# 操作文档 - 发动机总成号主数据维护 - AMS & IPT

> 在 AmSupply 系统中维护 MMSA 用于发送总装位置的 call off, 在 IPT 系统中维护 SDSA 用于在 ENG_A2 位置接收 call off。 数据的发送和接收情况可通过 EMFOR 命令查询。

# 操作步骤

## AmSupply 中维护零件 MMSA

### MM02

检查零件Procurement Type='F' 表示为采购件(AMS中采购, IPT中自制)。

Special Procurement为空, 表示为control零件。

![PixPin_2025-06-12_11-43-09](assets/PixPin_2025-06-12_11-43-09.png)

### DISCO - Initial Material Setup

`/DA0/DISCO - Dynamic Integrated Supply Cockpit`

![](assets/clip_image001-20211028191709-jf35n1c.png)

#### （090）Handle Worklist

优先选择 Select tasks 后输入零件号。 如果该零件不存在 task，则通过 Create tasks 创建。

![](assets/clip_image003-20211028191709-6p1jsvt.png)

![](assets/clip_image005-20211028191709-bxpnwnq.png)

![](assets/clip_image007-20211028191709-zf9xsvx.png)

#### （200）Dispatch MRP Controller

![](assets/clip_image009-20211028191709-y41lhy2.png)

点击![](assets/clip_image011-20211028191709-okm9hw6.png)维护正确的 Controller 代码。

选中零件行后点击![](assets/clip_image013-20211028191709-w2xi2z8.png)

![](assets/clip_image015-20211028191709-bm4ywe3.png)

如果零件状态![](assets/clip_image017-20211028191709-hgogrui.png)变为 P05 则进入步骤[（300）Determine Supplier](#HbqIC)，如果零件状态变为 300，则进入步骤[（400）Unloading Point](#GtlXO)。

#### （300）Determine Supplier

![](assets/clip_image019-20211028191709-meuq1xd.png)

点击![](assets/clip_image021-20211028191709-gawrb6x.png)选择供应商。15326564 是 BDA 工厂供应商，发动机总成请选择此项。

![](assets/clip_image023-20211028191709-ac2f9iq.png)

#### （400）Unloading Point

![](assets/clip_image025-20211028191709-6td9ltm.png)

选中行后点击![](assets/clip_image027-20211028191709-8wnkk3r.png)。

![](assets/clip_image029-20211028191709-wov2zi9.png)

点击![](assets/clip_image031-20211028191709-9zngb6y.png)维护卸货点。

MFA 工厂的发动机总成号卸货点为 `310X`，MRP Area 为 `104_AS4`

![](assets/clip_image033-20211028191709-m2j9pqa.png)

#### （500）Maintain Master Data

![](assets/clip_image035-20211028191709-tztxh9e.png)

选中行后点击![](assets/clip_image037-20211028191709-phdtyxc.png)，系统会加载默认值。

点击![](assets/clip_image039-20211028191709-8mz815i.png)对零件主数据进行最终检查。

![](assets/clip_image041-20211028191710-ttme9al.png)

#### （900）Active

![](assets/clip_image043-20211028191710-livics5.png)

选中行后点击![](assets/clip_image045-20211028191710-rxx7jzz.png)激活 MMSA。

![](assets/clip_image047-20211028191710-ruv39e4.png)

请记下系统所创建的 SA 号码。

![](assets/clip_image049-20211028191710-78vdnlb.png)

### ME32L 补充维护 MMSA 主数据

T-Code: `ME32L`

#### 调整 Partner 代码

![](assets/clip_image051-20211028191710-mifurmo.png)

调整 Partner Number 为 `015326564E`

![](assets/clip_image053-20211028191710-bwbt860.png)

#### Creation Profile

在详情页面调整 Creation Profile 为 `PGSS`

![](assets/clip_image055-20211028191710-24b3z4r.png)

![](assets/clip_image057-20211028191710-tlfu90v.png)

## IPT 中维护零件 SDSA

### MM02 主数据维护

![](assets/clip_image059-20211028191710-yv89tzs.png)

![](assets/clip_image061-20211028191710-0zhf4xd.png)

![](assets/clip_image063-20211028191710-3nxuni1.png)

![](assets/clip_image064-20211028191710-mxdymru.png)

![](assets/clip_image065-20211028191710-d4j256o.png)

![](assets/clip_image066-20211028191710-sggzf9j.png)

![](assets/clip_image068-20211028191710-8bh1l82.png)

![](assets/clip_image069-20211028191710-fbwtpch.png)

请根据实际情况调整 Safety time/act cov 的具体数值来控制提前期。

![](assets/clip_image070-20211028191710-d5r7096.png)

### VA31 创建 SDSA

`VA31 - Create Scheduling Agreement`

![](assets/clip_image072-20211028191710-5nudw59.png)

Sched.Agree.Engine：系统自动生成不用填写

Sold-to-part/ship-to-part： `015326564B` 对应 MFA 总装

PO Number： 填写此前在步骤（900）Active 中所得的 SA 号。

其他的如图填写。第一个发动机号有空格，后面 Customer Material Number 里发动机号无空格。

随后双击这条 item 进入详细设置

![](assets/clip_image074-20211028191710-6sxwjzr.png)

维护 Shipping 页下的卸货点等数据，如图。

![](assets/clip_image076-20211028191710-2pekpak.png)

维护 Billing Document 页下的数据，如图。

![](assets/clip_image078-20211028191710-j3kbf3s.png)

点击![](assets/clip_image080-20211028191710-z6xsryo.png)保存。

如出现提示，则选择 Edit。

![](assets/clip_image081-20211028191710-g2rkpes.png)

双击空白位置进入设置。

![](assets/clip_image083-20211028191710-moqidwy.png)

填下这部分参数。

![](assets/clip_image085-20211028191710-a6y88x5.png)

如果有试装需求，还需要在 VA32 中编辑 30000002，在最后一行添加新维护的发动机。内容可以参照其他的 item 内容。

## Call Off 内容检查

### AMS - Send

在 ME39 中查看历史发送记录。

![](assets/clip_image087-20211028191710-9djjo0q.png)

### IPT - Receive

EMFOR

![](assets/clip_image089-20211028191710-gvtjdm2.png)

可以看到选定日期的 call off 接收情况。

点击 IDoc 号可以查看详细内容。

![](assets/clip_image091-20211028191710-sq6otrq.png)

![](assets/clip_image093-20211028191710-2sbpolq.png)

# 常见问题解答

# 文档信息

> 李天宇 ([<EMAIL>](http://mailto:<EMAIL>))
>
> 初次发布时间：2019-10-16
>
> 最后更新时间：2025-06-12
>
> 版本: V2.3

‍

