﻿---
title: "AmS.BDA.Gaps.Export to Excel"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# AmS.BDA.Gaps.Export to Excel

# Key

目前 AMS 导出的 MHTML \ xls 等格式体积太大， 偶尔导出会失败。 (大约为 xlsx 的 10 倍) 而且导出成功后也需要转格式， 影响工作效率

目前 AMS 和 IPT 同样使用 ERP6.0 系统， 但没有安装 enhancement package, 不支持 xlsx 导出。

S4 HANA 在 2023 年更新前都没有计划升级强化包。

[📅20230710 EHP8 更新完成，可以下载 xlsx 格式的文件了。20230710085522 AMS 已支持下载 xlsx 格式文件.msg](#20230710085451-01y135q)

# Storyline

[AW\_ Gap in Exporting Function_5_11_2020.pdf](es://AW_%20Gap%20in%20Exporting%20Function_5_11_2020.pdf)

the XLSX extract function is missing because AmSupply is still running on an old SAP ERP 6.0 version. There are always discussions to install the latest Enhancement Package. But I don’t know the last discussions.

@Felipe: Can you give us an update when we can expect the installation of the latest Enhancement package on the AmSupply Systems?

📅20201106 ERP 版本问题(强化包)

📅20201109 查看 Enhancement Packages(强化包)版本方法  
​![image.jpg](assets/20201212140740-qn1h5n2-image.jpg)  
​![image.jpg](assets/20201212140825-i6bczsp-image.jpg)  
​​![image](assets/image-20230626090007-dto27yf.png)

上图为 📅20230626 新增

📅20201111 来自[Felipe](CRM/Felipe%20Biondo.md) 的答复  
​![image.jpg](assets/20201212140714-honxasp-image.jpg)

📅20201214 由[Christian Reuter](CRM/Christian%20Reuter.md)加入到 DRF list 中 [20211013 Re2 AW Re2 Gap in Exporting Function.msg](es://20211013%20Re2%20AW%20Re2%20Gap%20in%20Exporting%20Function.msg)

📅20230626 回忆，此 DRF 取消，等待 EHP 更新。预计 7 月第一周更新。

在 Parallel Effort 计算中，MRP 提出因为格式问题和额外一次的下载，会增加 0.15 个 HC [ Update Parallel operation effort](es://RE:%20Update%20Parallel%20operation%20effort)​[MRP extra workload from AMS V206 MFA online.xlsx](es://MRP%20extra%20workload%20from%20AMS%20V206%20MFA%20online.xlsx)，[20220720 RE Update Parallel operation effort.msg](es://20220720%20RE%20Update%20Parallel%20operation%20effort.msg)

📅20230710 EHP8 更新完成，可以下载 xlsx 格式的文件了。[20230710085522 AMS 已支持下载 xlsx 格式文件.msg](es://20230710085522%20AMS已支持下载xlsx格式文件.msg)

