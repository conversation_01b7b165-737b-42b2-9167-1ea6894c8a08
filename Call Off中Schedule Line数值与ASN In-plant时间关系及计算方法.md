﻿---
title: "Call Off中Schedule Line数值与ASN In-plant时间关系及计算方法"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# Call Off中Schedule Line数值与ASN In-plant时间关系及计算方法

📅20221213 ASN使用的Schedule Line并不是顺序的，在生成ASN后再生成的Schedule可能会插在ASN前面。

[RE 关于AMS 系统Calloff 导出 _20221213_020652.zip](es://RE%20关于AMS%20系统Calloff%20导出%20_20221213_020652.zip)

![image](assets/image-20221213140837-vvrhogo.png)​

📅20221208 Query WP3_009与ME39内容显示一致

Query: `SQ00- WP4 - WP4_Q_GR_COMP - Delivery date and GR posting date comparison`

![image.png](assets/image-20220513142852-6p5iwhq.png)

[20220303 回复 AMS CALLOFF 导出数据抓取错误修正.msg](es://20220303%20回复%20AMS%20CALLOFF%20导出数据抓取错误修正.msg)

---

曹工,

我整体说一下这个表里几个数字的关系.

X列Reduced是该Delivery总共收货的数量, Y列Open Qty是该Delivery剩余未收货的总数. W列的Quantity是HU级别收货的数量.

比如下表,

| Delivery  | Material         | Pstng Date | Time     | Registration date | Registration time | Delivery Quantity | Quantity | Quantity Reduced | Open Qty |
| --------- | ---------------- | ---------- | -------- | ----------------- | ----------------- | ----------------- | -------- | ---------------- | -------- |
| 500054019 | A  177 610 17 00 | 2022-3-2   | 09:12:50 | 2022-3-2          | 08:35:20          | 160               | 20       | 80.000           | 80.000   |
| 500054019 | A  177 610 17 00 | 2022-3-2   | 09:12:57 | 2022-3-2          | 08:35:20          | 160               | 20       | 80.000           | 80.000   |
| 500054019 | A  177 610 17 00 | 2022-3-2   | 09:13:02 | 2022-3-2          | 08:35:20          | 160               | 20       | 80.000           | 80.000   |
| 500054019 | A  177 610 17 00 | 2022-3-2   | 09:13:09 | 2022-3-2          | 08:35:20          | 160               | 20       | 80.000           | 80.000   |

转换成文字描述: Delivery 500054019中的零件A 177 610 17 00, 总共有160pcs(Delivery Quantity), 目前已经收货80pcs. 还有80pcs未收货.  具体是分别在09:12:50收货了20pcs. 09:12:57收货了20pcs…………

实际上数据表格应该为这样:

![](assets/clip_image002-20220303164732-xbrt4xr.jpg)

回到您举例的零件

![](assets/clip_image004-20220303164732-br1glcr.jpg)

9:18:35收货的零件数量在W列, 175pcs.

Registration是针对Delivery级别的, 所以应该使用的数值是U列的Delivery Quantity.

我们来实际的应用一下, 举一个覆盖很多可能型的例子, 假设是3月2日下载的表格:

| Delivery  | Material         | Pstng Date | Time     | Registration date | Registration time | Delivery Quantity | Quantity | Quantity Reduced | Open Qty |
| --------- | ---------------- | ---------- | -------- | ----------------- | ----------------- | ----------------- | -------- | ---------------- | -------- |
| 500054019 | A  177 610 17 00 | 2022-3-1   | 13:31:20 | 2022-3-1          | 09:15:20          | 120               | 20       | 40.000           | 80.000   |
| 500054019 | A  177 610 17 00 | 2022-3-2   | 09:32:50 | 2022-3-1          | 09:15:20          | 120               | 20       | 40.000           | 80.000   |
| 500054020 | A  177 610 17 00 | 2022-3-2   | 09:32:57 | 2022-3-2          | 09:25:20          | 140               | 20       | 20.000           | 120.000  |
| 500054021 | A  177 610 17 00 | 2022-3-2   | 09:13:02 | 2022-3-2          | 09:35:20          | 160               | 20       | 40.000           | 120.000  |
| 500054021 | A  177 610 17 00 | 2022-3-2   | 09:13:09 | 2022-3-2          | 09:35:20          | 160               | 20       | 40.000           | 120.000  |

第一行和第二行, Delivery 500054019, Registration Date是1号, 属于非在途.

第三行, Delivery 500054020, 属于在途, 应当从9:00的call off中减去Delivery  
Quantity 140pcs.

第四行和第五行  
Delivery 500054021, 属于在途. 应当从call off中减去Delivery Quantity 160pcs. 请注意这里只减去了一个160pcs.

函数我不就写了吧, 个人思路是首先判断是否属于在途, 属于在途的情况下使用Delivery Quantity, 可以用 ‘Delivery Quantity’ / countifs(‘Delivery’,’material’)

得到这样一个结果

| Delivery  | Material         | Pstng Date | Time     | Registration date | Registration time | Delivery Quantity | Quantity | Quantity Reduced | Open Qty |     |
| --------- | ---------------- | ---------- | -------- | ----------------- | ----------------- | ----------------- | -------- | ---------------- | -------- | --- |
| 500054019 | A  177 610 17 00 | 2022-3-1   | 13:31:20 | 2022-3-1          | 09:15:20          | 120               | 20       | 40.000           | 80.000   | 0   |
| 500054019 | A  177 610 17 00 | 2022-3-2   | 09:32:50 | 2022-3-1          | 09:15:20          | 120               | 20       | 40.000           | 80.000   | 0   |
| 500054020 | A  177 610 17 00 | 2022-3-2   | 09:32:57 | 2022-3-2          | 09:25:20          | 140               | 20       | 20.000           | 120.000  | 140 |
| 500054021 | A  177 610 17 00 | 2022-3-2   | 09:13:02 | 2022-3-2          | 09:35:20          | 160               | 20       | 40.000           | 120.000  | 80  |
| 500054021 | A  177 610 17 00 | 2022-3-2   | 09:13:09 | 2022-3-2          | 09:35:20          | 160               | 20       | 40.000           | 120.000  | 80  |

---

李天宇

Allen Li

第三方物流管理与持续改进

物流运营

Logistics Operation

Phone: +86 10 6782 4833

Email: [<EMAIL>](mailto:<EMAIL>)

Location: B2, 2nd Floor, B124, BBAC

**发件人:**  Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  2022年3月2日 15:45

**收件人:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
li yanyan 李妍妍（第三方优尼特） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>)); leng, johnny  
[[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:**  RE: AMS CALLOFF 导出数据抓取错误修正

Tianyu

A 206 815 15 00  这颗料CALL OFF 释放时间是 9:00:43 , Registration  
time 为 9:18:35, 按照逻辑这条对应的 175 应该要被抓成 3月2日的在途，但是因为这条的状态变成了C 状态 in plnt 且 Opent Qty 列没有数值，所以这条我们就没有抓到在途。

![](assets/clip_image005-20220303164732-ytj0wth.jpg)

Thanks

Best Regards,

Collin Cao /曹红莲

ATM Planning / 计划组

‍

**From:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Sent:**  Tuesday, March 1, 2022 5:11 PM

**To:**  Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Cc:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
li yanyan 李妍妍（第三方优尼特） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
leng, johnny [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Subject:**  回复:  
AMS CALLOFF 导出数据抓取错误修正

GR表中的是收货入库的时间.

inplant时间已经加入.

测试时, 之前的过滤器就先不要用了, 只通过delivery  
date作为限制条件.

导出的ASN数据中如果Registration是00000的则没有inplant.

![](assets/clip_image017-20220303164732-cbgnmhh.jpg)

---

李天宇

Allen Li

第三方物流管理与持续改进

物流运营

Logistics Operation

Phone: +86 10 6782 4833

Email: [<EMAIL>](mailto:<EMAIL>)

Location: B2, 2nd Floor, B124, BBAC

‍

‍

**发件人:**  Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  2022年2月28日 15:34

**收件人:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
li yanyan 李妍妍（第三方优尼特） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>)); leng, johnny  
[[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:**  RE: AMS CALLOFF 导出数据抓取错误修正

Tianyu

麻烦问下附件GR表中L 列的时间指的什么时间点 ,  如上附件能否加入一列 In Plnt 的日期和时间点，以便和CALL OFF 触发时间点做匹配，多谢。

按照目前的逻辑是CALL OFF 触发时间点之后A状态 In  
Plnt 的数据是需要算作在途的对吧，我们想导出所有A 状态的GR 然后再去匹配CALL OFF 释放的时间点，抓取在途数据。

Thanks

Best Regards,

Collin Cao /曹红莲

ATM Planning / 计划组

‍

**发件人:**  Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  2022年2月28日 14:08

**收件人:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
li yanyan 李妍妍（第三方优尼特） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>)); leng, johnny  
[[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:**  RE: AMS CALLOFF 导出数据抓取错误修正

Tianyu

好的，我理解导出ASN（GR）表的时间和CALL OFF 触发的时间一致 就不存在如下GAP 了对吧。

麻烦问下AMS系统 触发CALL OFF 的时间都是固定每天早上9：00不，谢谢。

Thanks

Best Regards,

Collin Cao /曹红莲

ATM Planning / 计划组

‍

**From:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Sent:**  Monday, February 28, 2022 1:47 PM

**To:**  Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Cc:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
li yanyan 李妍妍（第三方优尼特） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
leng, johnny [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Subject:**  回复:  
AMS CALLOFF 导出数据抓取错误修正

Call off时间早上9:00:37

ASN转为in-plant时间9:13:53

因此Call off时候310pcs是open的状态, 您下载ASN表格的时候则是in-plant.

![](assets/clip_image020-20220303164732-vj1rh60.jpg)

![](assets/clip_image022-20220303164732-pmi32bu.jpg)

附件是今日目前为止的in-plant记录.

---

李天宇

Allen Li

第三方物流管理与持续改进

物流运营

Logistics Operation

Phone: +86 10 6782 4833

Email: [<EMAIL>](mailto:<EMAIL>)

Location: B2, 2nd Floor, B124, BBAC

‍

**发件人:**  Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  2022年2月28日 13:26

**收件人:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
li yanyan 李妍妍（第三方优尼特） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>)); leng, johnny  
[[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:**  RE: AMS CALLOFF 导出数据抓取错误修正

Tianyu

按照您视频提示筛选如下条件导出GR表如附件2，经核对存在在途少抓取的情况，示例如下：

零件：  
A1776102700  ，  
104_BS4 ；  2月28日  CALL OFF 需求 310 , 在途状态为 A  In Plnt 带X ，在途未抓取到，3月3日需求  310 ，如附件3 的差异表，3月4日到货量为 620，查询MD 04 实际只需要到 310 ，麻烦您帮忙查看，谢谢。

![](assets/clip_image024-20220303164732-q9s2rze.jpg)

Thanks

Best Regards,

Collin Cao /曹红莲

ATM Planning / 计划组

‍

**发件人:**  Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  2022年2月22日 17:16

**收件人:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
li yanyan 李妍妍（第三方优尼特） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>)); leng, johnny  
[[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:**  RE: AMS CALLOFF 导出数据抓取错误修正

Tianyu

如下界面只选定需要导出的日期区间，导出GR信息如附件所示，C列增加了 In  
Plnt列，如之前确认的逻辑是否筛选出G列A 状态，C 列不带X 的数据 作为被减数，烦请您帮忙查看，多谢。

![](assets/clip_image035-20220303164732-9z78g9n.jpg)

Thanks

Best Regards,

Collin Cao /曹红莲

ATM Planning / 计划组

‍

**From:**  li tianyu 李天宇（物流部） [<EMAIL>](mailto:<EMAIL>)

**Sent:**  Tuesday, February 22, 2022 3:25 PM

**To:**  Cao, Collin [<EMAIL>](mailto:<EMAIL>)

**Cc:**  Wang, Tracy [<EMAIL>](mailto:<EMAIL>);  
Chen, Echo [<EMAIL>](mailto:<EMAIL>);  
li yanyan 李妍妍（第三方优尼特） [<EMAIL>](mailto:<EMAIL>);  
leng, johnny [<EMAIL>](mailto:<EMAIL>);  
yu pengyu 于朋雨 [<EMAIL>](mailto:<EMAIL>)

**Subject:**  回复:  
AMS CALLOFF 导出数据抓取错误修正

烦请测试一下.

有需要过滤的参数请告诉我.

---

李天宇

Allen Li

第三方物流管理与持续改进

物流运营

Logistics Operation

Phone: +86 10 6782 4833

Email: [<EMAIL>](mailto:<EMAIL>)

Location: B2, 2nd Floor, B124, BBAC

‍

‍

**发件人:**  Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  2022年2月18日 15:01

**收件人:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
leng, johnny [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
li yanyan 李妍妍（第三方优尼特） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:**  RE: AMS CALLOFF 导出数据抓取错误修正

Tianyu

MFA文件导出Code 如下：

CALLOFF 的T-code：：/DA0/0080_LAB

GR（ASN）的T-code：SQ00

DOH的T-code：MD06

您平时用哪个命令导出ASN, 我让IT的同事看看把inplant状态给放进去.

如果把inplant状态放进来，那我们抓取在途是否A/B状态都要考虑，还是只考虑A，谢谢。

Thanks

Best Regards,

Collin Cao /曹红莲

ATM Planning / 计划组

‍

‍

**From:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Sent:**  Friday, February 18, 2022 2:47 PM

**To:**  Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
leng, johnny [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Cc:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
li yanyan 李妍妍（第三方优尼特） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Subject:**  回复:  
AMS CALLOFF 导出数据抓取错误修正

曹工,

我检查了一下数据, 结论是A/B并不能完全代表货物的inplant状态.

我以inplant为前提筛选ASN, 发现部分零件的状态是A.

您平时用哪个命令导出ASN, 我让IT的同事看看把inplant状态给放进去.

![](assets/clip_image039-20220303164732-316bc7j.jpg)

---

李天宇

Allen Li

第三方物流管理与持续改进

物流运营

Logistics Operation

Phone: +86 10 6782 4833

Email: [<EMAIL>](mailto:<EMAIL>)

Location: B2, 2nd Floor, B124, BBAC

‍

‍

**发件人:**  Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  2022年2月16日 13:40

**收件人:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
leng, johnny [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:**  RE: AMS CALLOFF 导出数据抓取错误修正

Tianyu，

我建议咱们再用几个有B-ASN的零件计算试试. 目前的数值应该是需要减去A-ASN, 但是不要减去B-ASN的.

基于这条建议ASN只减A状态的ASN  Open Qty 的量，依据今天的CALLOFF和GR  信息进行2月21日 Demand 计算，发现有些零件跑出来的结果和MD 04对比有差异，麻烦您帮忙看下，多谢。

举例：零件  
A2477228601  （附件1中黄色标注），  
 GR 表（附件5中） ASN  Open Qty  14日 250  ，17日250，18日 250，依据A状态算在途扣减的逻辑，2月21日 不需要到货，但是MD04  2月21日有Schlne  
250 .

备注：另绿色标注的零件：A1776111400  依据如上逻辑计算的结果又是对的。

Thanks

Best Regards,

Collin Cao /曹红莲

‍

‍

**From:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Sent:**  Friday, February 11, 2022 4:17 PM

**To:**  leng, johnny [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Cc:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**Subject:**  回复:  
AMS CALLOFF 导出数据抓取错误修正

目前系统中没有导出这个数据的功能, Local IT答复没有权限添加, 如果需要德国IT来改动的话, 在审批通过的情况下需要3-6个月的时间.

我建议更改in-plant ASN的计算逻辑,就不改动系统了.

如果需要加急调整系统, 需要比较强的理由.

---

李天宇

Allen Li

第三方物流管理与持续改进

物流运营

Logistics Operation

Phone: +86 10 6782 4833

Email: [<EMAIL>](mailto:<EMAIL>)

Location: B2, 2nd Floor, B124, BBAC

‍

‍

**发件人:**  leng, johnny [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  2022年2月11日 14:39

**收件人:**  li tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>)); yu  
pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:** 回复: AMS CALLOFF 导出数据抓取错误修正

Tianyu，

有哪个tcode  
可以导出和第一列红框标注的数据一致的excel吗？现在的tcode导出时跟第二红框数据一致。

Thanks

Best Regards,

**Johnny / Leng **

ATM Control Tower Assistant Manager

‍

‍

**发件人:**  li tianyu 李天宇（物流部）  
[[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  Friday, February  
11, 2022 2:04 PM

**收件人:**  leng, johnny [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>)); yu  
pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:** 回复: AMS CALLOFF 导出数据抓取错误修正

目前Local IT答复没有权限处理, 如果需要德国IT改动的话, 挺麻烦的.

我建议咱们再用几个有B-ASN的零件计算试试. 目前的数值应该是需要减去A-ASN, 但是不要减去B-ASN的.

然后我们调整一下计算逻辑吧.

---

李天宇

Allen Li

第三方物流管理与持续改进

物流运营

Logistics Operation

Phone: +86 10 6782 4833

Email: [<EMAIL>](mailto:<EMAIL>)

Location: B2, 2nd Floor, B124, BBAC

‍

‍

**发件人:**  leng, johnny [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  2022年2月11日 13:52

**收件人:**  yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>)); li  
tianyu 李天宇（物流部） [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:** 回复: AMS CALLOFF 导出数据抓取错误修正

Dear Tianyu，

劳烦这个事问下进展。谢谢。

@pengyu，是的可以临时改动，但是inplant逻辑和实际系统的数据是有背，后续会出大问题。

Thanks

Best Regards,

**Johnny / Leng **

ATM Control Tower Assistant Manager

‍

‍

**发件人:**  yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  Friday, February  
11, 2022 1:48 PM

**收件人:**  leng, johnny [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:** 答复: AMS CALLOFF 导出数据抓取错误修正

你直接发给天宇哥，抄上我就行，昨天我理解的是你们说回去改自己的，不用他做啥。可能我没理解明白。

**发件人:**  leng, johnny [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**发送时间:**  2022年2月11日 13:46

**收件人:**  yu pengyu 于朋雨 [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**抄送:**  Wang, Tracy [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Cao, Collin [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>));  
Chen, Echo [[<EMAIL>](mailto:<EMAIL>)](%<EMAIL>%5D(mailto:<EMAIL>))

**主题:**  AMS CALLOFF 导出数据抓取错误修正

Hello pengyu，

Tianyu咱们聊的那个事 AMS CALLOFF 导出数据抓取错误修正，后续calloff excel中抓取左边第一个框那列。

帮忙问下tianyu有进展，这个逻辑对demand有较大的影响。

Thanks

‍

