﻿---
title: "AmS.BDA.PFM"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "AMS"

category: "Project"
project: "AMS系统"
---
# AmS.BDA.PFM

# ⭐️ Key

PFM是一个由PP lead的系统和装焊零件配送模式改造项目, 通过AMS/PLUS/ASF系统功能开发来在装焊车间实现类似JIS的物流配送方式.

>> 为减少装焊车间线边及库房车间, 将车身下级件生产分解为多个独立订单进行排序, 以在装焊车间实现类似JIS的配送方式.
>>
>> PFM并不是独立系统, 而是Plus,ASF,AMS的功能扩展.最开始由Bremen提出并实施, 解决其车身工厂车身变量太多需要大量不同种类的零件堆放在现场的问题.
>>
>> 目前在BBAC由PP lead作为独立项目进行, 在顺义工厂进行小产量车型测试. 目前计划在BDA随V254车型上线. AmSupply及其他系统为其配合.
>>

PFM卸货点为特殊配置，不可与其他JIT零件混用。[20230417103747 答复 The usage for 520X508X.msg](es://20230417103747%20答复%20The%20usage%20for%20520X508X.msg)

![image](assets/image-20230417103916-ej7bym6.png)

EDI中"消费地点"字段, 为PFM所需专用.

![image](assets/image-20231009155426-fbcczkv.png)![image](assets/image-20231009155445-0mjjtrq.png)​​

# 📆 Storyline

20210326

预计 2021 年同 V254/V214 一起上线

[REprojectPFMforspecialdemandsforparts.eml](assets/20201212170758-4oiaqkg-RE_%20project%20PFM%20for%20special%20demands%20for%20parts.eml)

20210511 在AMS BDA 项目时间调整方案讨论Timeline reschedule Postpone MRA Engine展示给冯智时, 其咨询其中提到的PFM.

20210512 检查 `20210415_PFM_Bible_-_EN-20210512110447-jvy8wfg.ppt`  并咨询李明奎后, 总结 PFM 功能如下

> 为减少装焊车间线边及库房车间, 将车身下级件生产分解为多个独立订单进行排序, 以在装焊车间实现类似JIS的配送方式.
>
> PFM并不是独立系统, 而是Plus,ASF,AMS的功能扩展.最开始由Bremen提出并实施, 解决其车身工厂车身变量太多需要大量不同种类的零件堆放在现场的问题.
>
> 目前在BBAC由PP lead作为独立项目进行, 在顺义工厂进行小产量车型测试. 目前计划在BDA随V254车型上线. AmSupply及其他系统为其配合.

📅20230110 目前254使用PFM系统的生产计划应由生产计划科肖凯通知装焊车间董照龙，由后者码放进入系统。

本例中后者码放SOP需求在1月份，实际生产是在2月份。 [答复 V254 sop 时间_20230110_111616.zip](es://答复%20V254%20sop%20时间_20230110_111616.zip)

📅20231218 吴晨咨询, 能否给V206的也设置PFM. Email咨询梁雨潇.

