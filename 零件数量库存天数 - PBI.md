﻿---
title: "零件数量库存天数 - PBI"
summary: "待补充文档摘要"
date: "2021-01-01"
updated: "2021-01-01"
status: "draft"
tags:
  - "待分类"
category: "Project"
project: "系统优化"
---
# 零件数量库存天数 - PBI

## 根据SUT判断GLTKLT

IPT 根据 SUT 前缀判断: 0 开头是 GLT, 1 开头是 KLT

```M
= Table.AddColumn(已添加自定义2, "Type", each if Text.StartsWith([SUT], "1") then "GLT" else if Text.StartsWith([SUT], "0") then "KLT" else "TBD")
```

AMS 根据 SUT 前缀判断:  
	BDA : V 开头是 GLT, U 开头是 KLT  
	SHU : E 开头是 GLT，F 开头是 KLT

```M
# BDA
= Table.AddColumn(已添加自定义, "Type", each if Text.StartsWith([SUT], "V") then "GLT" else if Text.StartsWith([SUT], "U") then "KLT" else "TBD")
# SHU
= Table.AddColumn(已添加自定义, "Type", each if Text.StartsWith([SUT], "E") then "GLT" else if Text.StartsWith([SUT], "F") then "KLT" else "TBD")
```

对于同时存在 KLT 和 GLT 的零件，视为 KLT 零件:

```m
# 首先透视列Type,  之后新建列
= if [KLT]>0 then "KLT" else if [GLT]>0 then "GLT" else "TBD"
```


‍## 库存天数计算

> 已知问题
>
> - 实际库存的偏差
>
>   - 没有考虑线边零件
>   - 已经剔除近期内无需求的零件 (例如 EOP 零件, 长期不用件, ECC 新旧件, 试装零件)
>   - 已经剔除 104 位置的零件 (例如 试装零件)
>   - 轴瓦/买卖件等特殊零件没有统计在内
>
> - 实际日均需求的偏差 (但比例很低, 对 DOH 的影响<1 天)
>
>   - 受限于数据下载难度, 使用未来 10 天左右的需求数据求均值, 而不是真实的长期的零件需求
>   - 使用"存在需求"的天数作为分母, 而不是自然日 (Num of Days has Demand-1)
>   - 轴瓦/买卖件等特殊零件没有统计在内
>
> - EP1 和 EP2 部分通用件因为需求在一个 MRP_AREA 所以计算不准确.

> 注意
>
> - UNLP 使用时, 同一个 SLOC 下多个 UNLP, 在只需要 SLOC 和 MRP Area 的情况下需要先去重

# 1 基础数据文件

从 AMS 的原始数据要尽可能完整，之后再通过 PowerQuery 删减。

## 1.1 库存

### 1.1.1 数据获取

通过后台 JOB 定时生成数据, 之后通过 SM37 下载.

| SYS          | 数据来源                    | Variant 名称 | Program                        | Layout 名称 | SM37 JOB                       |
| ------------ | --------------------------- | ------------ | ------------------------------ | ----------- | ------------------------------ |
| PSV<br />PSW | LX03                        | RPA-LITY     | RLS10030                       | DOH         | RPA - STOCK LX03               |
| IPT          | SQ01-WM_USER-WAREHOUSESTOCK | None         | AQA0WM_USER=====WAREHOUSESTOCK | /RPA        | AQA0WM_USER=====WAREHOUSESTOCK |

AMS

![image](assets/image-20230918144234-0p2nmmd.png)

IPT

![image](assets/image-20230921111046-ng3fcrm.png)

### 1.1.2 数据处理

原始数据约 28 万行
![image](assets/image-20240606091941-ydiousy.png)

**文件操作**

将 SM37 中下载的 txt 文件移动到 WarehouseStock 目录下, AMS 和 IPT 需要分别存放

![image](assets/image-20231020102831-kp5m3ci.png)

运行 python 程序消除前面的无效行.

![image](assets/image-20231020095020-5hl7th7.png)![image](assets/image-20231020103049-cwrq3mt.png)

Txt 文件复制到 archive 目录下归档.

重命名文件与 PowerQuery 所需一致.

![image](assets/image-20231020103138-wcsfhab.png)

**PowerQuery 操作**

之后使用 PowerQuery [Modyfy_Stock_PQ.xlsx](es://Modyfy_Stock_PQ.xlsx), 获取 FIN Stock {date}.csv 文件. 数据量较大, 大概需要运行半个小时.

> PBI 主要操作如下:
>
> 1. 数据清理![image](assets/image-20240606085417-uusp741.png)
>
>    1. Trim data
>    2. 调整列名一致
>    3. [根据 SUT 判断 GLTKLT](零件数量库存天数%20-%20PBI/根据SUT判断GLTKLT.md) - 该数据目前没有在 report 中使用.![image](assets/image-20240606090228-xyodt7u.png)
>    4. 获取存放时间 (AMS 直接获取, IPT 根据{today}-GR_Date 计算) - 该数据目前没有在 report 中使用.
>    5. 根据系统添加 Plant 列(104/1046)
>
> 2. 计算 HU 数量![image](assets/image-20240606085745-tsoqsb6.png)
> 3. 根据 ((20231020102920-1ef9qx5 "Warehouse Master")) 判断零件的 MRP Area 及 MRP Relavent
> 4. IPT 没有 Til, 会根据当前日期和 GR 日期计算
> 5. 通过 数据透视/分组 汇总同一个 MRP Area 下的零件库存数量
> 6. 📅20240614 按照刘欣的要求拆分 ENG 为 EP1, EP2, 同时其他厂区也都细分了 AS, BS. 但是与其他厂区不同, ENG 的需求只在 MRP_ENG_A2, 所以对于通用件计算不准确.
>    不过经过实际检查, 通用数量不多(<10 种), 因此暂时忽视.

‍

## 1.2 需求

### 1.2.1 数据获取

| SYS               | 数据来源                      | Variant 名称                                                                                                                                                 | Program                        | Layout 名称                                | JOB                   | 主要字段                       |
| ----------------- | ----------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------ | ------------------------------ | ------------------------------------------ | --------------------- | ------------------------------ |
| AMS<br />-PSW/PSV | SQ00<br /> - WP2 - MD04BSLIST | RPA-LITY : 1-11 天, TBET, Factory Calendar                                                                                                                   | AQA0WP2=========MD04BSLIST==== |                                            | RPA - DEMAND - MD04BS | Material，SLoc，Reqmt Date     |
| AMS<br />-PSW/PSV | SQ00<br /> - WP2 - MD04ASLIST | RPA-LITY : 1-11 天, Factory Calendar                                                                                                                         | AQA0WP2=========MD04ASLIST==== | DOH                                        | RPA - DEMAND - MD04AS | Material, MRP Area, qty, date, |
| IPT               | SQ01<br /> - MRP_PROC-DEPREQ  | 数据量太大, 因此拆分了三个.<br />RPA-LITY1 : 1-3 天<br />RPA-LITY2 : 4-5 天<br />RPA-LITY3 : 8-9 天(间隔大点, 获取长周期需求数据)<br />Python 程序会负责合并 | AQA0MRP_PROC====DEPREQ======== | /RPA<br />只保留 open>0, <br />非 deletion | RPA - DEMAND1/2/3     | Material, MRP Area, qty, date, |

![image](assets/image-20230926083916-wpkyvhl.png)

![image](assets/image-20230918152442-ioy8w8w.png)![image](assets/image-20230918154012-id4si1n.png)

### 1.2.2 数据处理

**文件操作**

IPT 数据量太大, 分批处理. IPT 的 pyton 会自动合并 txt 数据.

首先, 使用 python 消除没用的前几行数据. AMS 和 IPT 均为删除前 4 行, 之后的第二行. 得到的文件复制一份到 Archive 文件夹中. 然后文件改名,得到:

![image](assets/image-20230926144527-pgta9jq.png)![image](assets/image-20240606093921-9b444ud.png)

**PowerQuery 操作**

使用![image](assets/image-20231026165319-it7fssf.png)合并 IPT 的数据, 大约需要 30 分钟. 得到![image](assets/image-20231020114257-kar8asv.png).

检查文件内的数据日期, 确保数据正确更新.

![image](assets/image-20231026170120-4qwqlxk.png)

处理合并 Demand 文件得到![image](assets/image-20231020150924-i8v0qa6.png), 放到 DOH 文件夹下.

导出 csv, 放入 [1.4 Material Master](#20230918155814-5ksa4lp) 中, 根据 SOBSL 添加 Target MRP Area

> PBI 主要操作如下:
>
> 1. Trim data
> 2. 调整列明一致
> 3. 通过 数据透视/分组 汇总同一个 MRP Area 同一个日期下的零件需求
> 4. 统计需求总数, 以及出现需求的日期数量, 根据 Total Demand / (Num of Days has Demand-1)获得 Daily Demand
>    ![image](assets/image-20240606095029-9l7k0zw.png)

## 1.3 Call Off

数据量约 2 万

![image](assets/image-20240606122014-9uk12gd.png)

### 1.3.1 数据获取

| SYS         | 数据来源                   | Variant 名称                                   | Program                        | Layout 名称 | JOB            | 主要字段                                                            | 处理 |
| :---------- | :------------------------- | ---------------------------------------------- | ------------------------------ | ----------- | -------------- | ------------------------------------------------------------------- | ---- |
| AMS_PSW/PSV | SQ00 - WP3 - CALL_OFF_V1   | RPA-LITY<br />过去 3 个工作日<br />未来 1 个月 | AQA0WP3_MRP=====CALL_OFF_V1=== |             | RPA - CALL OFF | Material, release date, schedule line date, qty, unlp, sloc, vendor |      |
| IPT         | SQ01_MRP_PROC_CALL_OFF_VAL | RPA-lity                                       | AQA0MRP_PROC====CALL_OFF_VAL== |             | RPA - CallOff  |                                                                     |      |

![image](assets/image-20230919170340-84dfa5a.png)

### 1.3.2 数据处理

将下载的 txt 文件移动到 call off 目录下

![image](assets/image-20231020094643-0gzwkrw.png)

运行 python 程序消除前 4 行

![image](assets/image-20231020095020-5hl7th7.png)

得到\_py 尾缀文件

![image](assets/image-20231020095042-e4oqq1y.png)

复制到 archive 文件夹中归档

重命名文件

![image](assets/image-20231020095738-25k9fhk.png)

之后使用 [PQ_CallOff.xlsx](es://PQ_CallOff.xlsx), 全部刷新: 合并这三个文件, 删除无用列, 得到:![image](assets/image-20231020100316-lv9qrz2.png), 放到 DOH 文件夹下.

> Exclude "Beijing Benz", 自制件.
>
> (15326564, 90890000, 90890005)

## 1.4 Material Master

### 1.4.1 MRP Area 主数据

使用 [Material Master PQ.xlsx](es://Material%20Master%20PQ.xlsx) 处理获取的 csv 文件.

SOBSL : 得到![image](assets/image-20231020152937-6s2wq10.png), 按系统分开去获取 SOBSL.xlsx 数据, IPT 不用. 保存为![image](assets/image-20231020153107-fqx1jf1.png)

![image](assets/image-20231007133432-td16fdf.png)![image](assets/image-20231007133633-mud7ci1.png)![image](assets/image-20231117143309-8fmglz8.png)

回到 [Material Master PQ.xlsx](es://Material%20Master%20PQ.xlsx)

> 预处理 Stock 和 Demand, 考虑 SOBSL 更改零件需求的 MRP Area, 合并 stock 数据, 合并![image](assets/image-20231020153404-v18pw85.png)中的供应商信息
>
> ![image](assets/image-20240607092318-94neudt.png)

保存 [DOH Raw.csv](es://DOH%20Raw.csv), 和[DOH Group.csv](es://DOH%20Group.csv)

使用 python 备份文件到 Archive, 将旧数据 ZIP, 删除旧的.

![image](assets/image-20231026172327-vvwwljq.png)

### 1.4.2 包装主数据

使用 DOH RAW 结果清单里缺少 package 信息的零件, 否则数据量太大.

步骤:

使用 WP4 report 导出零件的 HU `WP4_CON_BLGBGR`

![image](assets/image-20241125113156-omtue9p.png)

如果没有 packmatls

使用[VEKP, PackageInstru 和 HU 的关系](Projects/AmS@BDA/数据获取/Table%20SE16N.md#20240612091557-65zyhz6)导出零件的长宽高

![image](assets/image-20241125110533-0u0zb9f.png)

## 1.5 ASN in ContainerYard

需要咨询[刘佳](CRM/刘佳.md)目前堆场内的箱子号.

PSV 和 PSW 使用 SQ00-WP4_CON_BLGBGR 导出数据.PSV 数据量太大, 只导出 ASN Delivery Date<Current + 14 的未收货箱子, 且只导出 KD 零件

JOB 名称 `AQA0WP4=========WP4_CON_BLGBGR`

IPT 使用 SQ01-MRP_PROC-CONT_BLG_BASIS

JOB 名称 `AQA0MRP_PROC====CONT_BLG_BASIS`

在 Stock 的 PowerQuery 中处理.目前因为 SYSU, 顺义不参与 ASN 的计算.

# 2 PBI 报表

![image](assets/image-20231020154505-ldzjhb8.png)合并 RAW 到 CUM 下面.保存 RAW 备份到 Archive

使用 [DOH with Value_Space.pbix](es://DOH%20with%20Value_Space.pbix), 调整切片器.

# 其他

## Warehouse Master

[PBI 步骤 warehouse master - 20230921.pdf](es://PBI步骤%20warehouse%20master%20-%2020230921.pdf)

导出 xlsx 存放在 `warehouse_master`

[generate_warehouse_master_20230921.pbix](es://generate_warehouse_master_20230921.pbix)

T320A
/DA0/0090_HBERID
/DA0/MM_ABLLFA
MDLG
T001L

‍

‍

## 📆StoryLine

📅20240709 展示给潘超看

- [x] 增加报废在 overview
- [x] 增加报废在单独的 sheet
- [x] 更改 Harbor 描述为 TJ-HarborYard

‍

